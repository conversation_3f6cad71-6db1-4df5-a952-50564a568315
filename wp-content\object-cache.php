<?php
/**
 * Simple object cache replacement
 * This file replaces the corrupted object-cache.php
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Simple object cache implementation
if (!class_exists('WP_Object_Cache')) {
    class WP_Object_Cache {
        private $cache = array();
        
        public function add($key, $data, $group = 'default', $expire = 0) {
            if (wp_suspend_cache_addition()) {
                return false;
            }
            
            if (empty($group)) {
                $group = 'default';
            }
            
            $id = $key;
            if ($this->multisite && !isset($this->global_groups[$group])) {
                $id = $this->blog_prefix . $key;
            }
            
            if ($this->_exists($id, $group)) {
                return false;
            }
            
            return $this->set($key, $data, $group, (int) $expire);
        }
        
        public function set($key, $data, $group = 'default', $expire = 0) {
            if (empty($group)) {
                $group = 'default';
            }
            
            if (is_object($data)) {
                $data = clone $data;
            }
            
            $this->cache[$group][$key] = $data;
            return true;
        }
        
        public function get($key, $group = 'default', $force = false, &$found = null) {
            if (empty($group)) {
                $group = 'default';
            }
            
            if (isset($this->cache[$group][$key])) {
                $found = true;
                if (is_object($this->cache[$group][$key])) {
                    return clone $this->cache[$group][$key];
                } else {
                    return $this->cache[$group][$key];
                }
            }
            
            $found = false;
            return false;
        }
        
        public function delete($key, $group = 'default') {
            if (empty($group)) {
                $group = 'default';
            }
            
            if (isset($this->cache[$group][$key])) {
                unset($this->cache[$group][$key]);
                return true;
            }
            
            return false;
        }
        
        public function flush() {
            $this->cache = array();
            return true;
        }
        
        private function _exists($key, $group) {
            return isset($this->cache[$group]) && (isset($this->cache[$group][$key]) || array_key_exists($key, $this->cache[$group]));
        }
    }
}

// Initialize the cache
$wp_object_cache = new WP_Object_Cache();

// Cache functions
if (!function_exists('wp_cache_add')) {
    function wp_cache_add($key, $data, $group = '', $expire = 0) {
        global $wp_object_cache;
        return $wp_object_cache->add($key, $data, $group, (int) $expire);
    }
}

if (!function_exists('wp_cache_set')) {
    function wp_cache_set($key, $data, $group = '', $expire = 0) {
        global $wp_object_cache;
        return $wp_object_cache->set($key, $data, $group, (int) $expire);
    }
}

if (!function_exists('wp_cache_get')) {
    function wp_cache_get($key, $group = '', $force = false, &$found = null) {
        global $wp_object_cache;
        return $wp_object_cache->get($key, $group, $force, $found);
    }
}

if (!function_exists('wp_cache_delete')) {
    function wp_cache_delete($key, $group = '') {
        global $wp_object_cache;
        return $wp_object_cache->delete($key, $group);
    }
}

if (!function_exists('wp_cache_flush')) {
    function wp_cache_flush() {
        global $wp_object_cache;
        return $wp_object_cache->flush();
    }
}
?>

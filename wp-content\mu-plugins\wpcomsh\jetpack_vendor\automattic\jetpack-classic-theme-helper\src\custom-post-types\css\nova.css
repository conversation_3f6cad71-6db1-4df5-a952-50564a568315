/* edit-items.css
-------------------------------------------------------------- */

.widefat .menu-label-row td {
	border-bottom-width: 1px;
}

.widefat .menu-label-row td h3 {
	padding-left: 30px;
}

.widefat .menu-label-row td h3 .edit-nova-section {
	font-size: .8em;
	font-weight: 400;
	margin-left: 5px;
}

.widefat .menu-order-value {
	width: 2.5em;
	text-align: center;
}

.widefat .menu-label-row, .widefat .menu-label-row td {
	background-color: #f0f0f1;
	color: #111;
	border: 0 none;
	box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.05);
}

.ui-sortable .type-nova_menu_item {
	cursor: move;
	background-color: #fff;
}

.ui-sortable .type-nova_menu_item:nth-child(even) {
	background-color: #f6f7f7;
}

.ui-sortable .type-nova_menu_item.ui-sortable-helper {
	box-shadow: 0 0 1px rgba(0, 0, 0, 0.1);
}

.tablenav .button-reorder {
	margin-top: 4px;
}

.tablenav .view-switch a, .tablenav div.tablenav-pages {
	display: none;
}


/* many-items.css
-------------------------------------------------------------- */

.many-items-table th, .many-items-table td {
	width: 30%;
}

.many-items-table th.nova-price, .many-items-table td.nova-price {
	width: 10%;
}

.many-items-table input, .many-items-table textarea {
	width: 100%;
}

.many-items-table input[type="file"] {
	box-sizing: border-box;
}


/* new
-------------------------------------------------------------- */

#the-list tr td:nth-of-type(2) {
	padding-top: 15px;
}

.nova-move-menu-up::before,
.nova-move-menu-down::before {
	margin-right: 5px;
	font: 400 10px/1 dashicons !important;
	speak: none;
}

.nova-move-menu-up::before {
	content: "\f342";
}

.nova-move-menu-down::before {
	content: "\f346";
}

.dashicon::before {
	font: 400 20px/1 dashicons;
	speak: none;
	top: 5px;
	display: inline-block;
	position: relative;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	text-decoration: none !important;
	vertical-align: top;
}

.dashicon-plus::before {
	content: "\f132";
}

.dashicon-edit::before {
	margin: 2px 5px 0 10px;
	content: "\f327";
	font-size: 10px;
}

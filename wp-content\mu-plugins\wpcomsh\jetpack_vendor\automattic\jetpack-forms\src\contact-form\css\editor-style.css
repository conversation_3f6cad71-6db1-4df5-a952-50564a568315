/* ==========================================================================
** Card
** ======================================================================= */

.card {
	display: block;
	position: relative;
	margin: 0 auto;
	padding: 16px;
	box-sizing: border-box;
	background: #fff;
	box-shadow: 0 0 0 1px rgba(200, 215, 225, 0.5), 0 1px 2px #e9eff3;
}

.card::after {
	content: ".";
	display: block;
	height: 0;
	clear: both;
	visibility: hidden;
}

@media ( min-width: 481px ) {

	.card {
		padding: 24px;
	}
}

.card > div {
	margin-top: 24px;
}

.card > div:first-child {
	margin-top: 0;
}


/* ==========================================================================
** Labels
** ======================================================================= */

label {
	display: block;
	font-size: 14px;
	font-weight: 600;
	margin-bottom: 5px;
}


/* ==========================================================================
** Text Inputs
** ======================================================================= */

input[type="text"],
input[type="tel"],
input[type="email"],
input[type="url"] {
	border-radius: 0;
	appearance: none;
	box-sizing: border-box;
	margin: 0;
	padding: 7px 14px;
	width: 100%;
	color: #2e4453;
	font-size: 16px;
	line-height: 1.5;
	border: 1px solid #c8d7e1;
	background-color: #fff;
	transition: all .15s ease-in-out;
	box-shadow: none;
}

input[type="text"]::placeholder,
input[type="tel"]::placeholder,
input[type="email"]::placeholder,
input[type="url"]::placeholder {
	color: #87a6bc;
}

input[type="text"]:hover,
input[type="tel"]:hover,
input[type="email"]:hover,
input[type="url"]:hover {
	border-color: #a8bece;
}

input[type="text"]:focus,
input[type="tel"]:focus,
input[type="email"]:focus,
input[type="url"]:focus {
	border-color: #0087be;
	outline: none;
	box-shadow: 0 0 0 2px #78dcfa;
}

input[type="text"]:focus::-ms-clear,
input[type="tel"]:focus::-ms-clear,
input[type="email"]:focus::-ms-clear,
input[type="url"]:focus::-ms-clear {
	display: none;
}

input[type="text"]:disabled,
input[type="tel"]:disabled,
input[type="email"]:disabled,
input[type="url"]:disabled {
	background: #f3f6f8;
	border-color: #e9eff3;
	color: #a8bece;
	-webkit-text-fill-color: #a8bece;
}

input[type="text"]:disabled:hover,
input[type="tel"]:disabled:hover,
input[type="email"]:disabled:hover,
input[type="url"]:disabled:hover {
	cursor: default;
}

input[type="text"]:disabled::placeholder,
input[type="tel"]:disabled::placeholder,
input[type="email"]:disabled::placeholder,
input[type="url"]:disabled::placeholder {
	color: #a8bece;
}


/* ==========================================================================
** Textareas
** ======================================================================= */

textarea {
	border-radius: 0;
	appearance: none;
	box-sizing: border-box;
	margin: 0;
	padding: 7px 14px;
	height: 92px;
	width: 100%;
	color: #2e4453;
	font-size: 16px;
	line-height: 1.5;
	border: 1px solid #c8d7e1;
	background-color: #fff;
	transition: all .15s ease-in-out;
	box-shadow: none;
}

textarea::placeholder {
	color: #87a6bc;
}

textarea:hover {
	border-color: #a8bece;
}

textarea:focus {
	border-color: #0087be;
	outline: none;
	box-shadow: 0 0 0 2px #78dcfa;
}

textarea:focus::-ms-clear {
	display: none;
}

textarea:disabled {
	background: #f3f6f8;
	border-color: #e9eff3;
	color: #a8bece;
	-webkit-text-fill-color: #a8bece;
}

textarea:disabled:hover {
	cursor: default;
}

textarea:disabled::placeholder {
	color: #a8bece;
}


/* ==========================================================================
** Checkboxes
** ======================================================================= */

input[type="checkbox"] {
	-webkit-appearance: none;
	display: inline-block;
	box-sizing: border-box;
	margin: 2px 0 0;
	width: 16px;
	height: 16px;
	float: left;
	outline: 0;
	padding: 0;
	box-shadow: none;
	background-color: #fff;
	border: 1px solid #c8d7e1;
	color: #2e4453;
	font-size: 16px;
	line-height: 0;
	text-align: center;
	vertical-align: middle;
	appearance: none;
	transition: all .15s ease-in-out;
	clear: none;
	cursor: pointer;
}

input[type="checkbox"]:checked::before {
	content: '\f147';
	font-family: dashicons;
	margin: -3px 0 0 -4px;
	float: left;
	display: inline-block;
	vertical-align: middle;
	width: 16px;
	font-size: 20px;
	line-height: 1;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	speak: none;
	color: #00aadc;
}

input[type="checkbox"]:disabled:checked::before {
	color: #a8bece;
}

input[type="checkbox"]:hover {
	border-color: #a8bece;
}

input[type="checkbox"]:focus {
	border-color: #0087be;
	outline: none;
	box-shadow: 0 0 0 2px #78dcfa;
}

input[type="checkbox"]:disabled {
	background: #f3f6f8;
	border-color: #e9eff3;
	color: #a8bece;
	opacity: 1;
}

input[type="checkbox"]:disabled:hover {
	cursor: default;
}

input[type="checkbox"] + span {
	display: block;
	font-weight: 400;
	margin-left: 24px;
}


/* ==========================================================================
** Radio buttons
** ======================================================================== */

input[type="radio"] {
	color: #2e4453;
	font-size: 16px;
	border: 1px solid #c8d7e1;
	background-color: #fff;
	transition: all .15s ease-in-out;
	box-sizing: border-box;
	-webkit-appearance: none;
	clear: none;
	cursor: pointer;
	display: inline-block;
	height: 16px;
	margin: 2px 4px 0 0;
	float: left;
	outline: 0;
	padding: 0;
	text-align: center;
	vertical-align: middle;
	width: 16px;
	min-width: 16px;
	appearance: none;
	border-radius: 50%;
	line-height: 10px;
}

input[type="radio"]:hover {
	border-color: #a8bece;
}

input[type="radio"]:focus {
	border-color: #0087be;
	outline: none;
	box-shadow: 0 0 0 2px #78dcfa;
}

input[type="radio"]:focus::-ms-clear {
	display: none;
}

input[type="radio"]:checked::before {
	float: left;
	display: inline-block;
	content: '\2022';
	margin: 3px;
	width: 8px;
	height: 8px;
	text-indent: -9999px;
	background: #00aadc;
	vertical-align: middle;
	border-radius: 50%;
	animation: grow .2s ease-in-out;
}

input[type="radio"]:disabled {
	background: #f3f6f8;
	border-color: #e9eff3;
	color: #a8bece;
	opacity: 1;
	-webkit-text-fill-color: #a8bece;
}

input[type="radio"]:disabled:hover {
	cursor: default;
}

input[type="radio"]:disabled::placeholder {
	color: #a8bece;
}

input[type="radio"]:disabled:checked::before {
	background: #e9eff3;
}

input[type="radio"] + span {
	display: block;
	font-weight: 400;
	margin-left: 24px;
}

@keyframes grow {

	0% {
		transform: scale(0.3);
	}

	60% {
		transform: scale(1.15);
	}

	100% {
		transform: scale(1);
	}
}

@keyframes grow {

	0% {
		transform: scale(0.3);
	}

	60% {
		transform: scale(1.15);
	}

	100% {
		transform: scale(1);
	}
}


/* ==========================================================================
** Selects
** ======================================================================== */

select {
	background: #fff url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiIHN0YW5kYWxvbmU9Im5vIj8+PHN2ZyB3aWR0aD0iMjBweCIgaGVpZ2h0PSIyMHB4IiB2aWV3Qm94PSIwIDAgMjAgMjAiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayIgeG1sbnM6c2tldGNoPSJodHRwOi8vd3d3LmJvaGVtaWFuY29kaW5nLmNvbS9za2V0Y2gvbnMiPiAgICAgICAgPHRpdGxlPmFycm93LWRvd248L3RpdGxlPiAgICA8ZGVzYz5DcmVhdGVkIHdpdGggU2tldGNoLjwvZGVzYz4gICAgPGRlZnM+PC9kZWZzPiAgICA8ZyBpZD0iUGFnZS0xIiBzdHJva2U9Im5vbmUiIHN0cm9rZS13aWR0aD0iMSIgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIiBza2V0Y2g6dHlwZT0iTVNQYWdlIj4gICAgICAgIDxnIGlkPSJhcnJvdy1kb3duIiBza2V0Y2g6dHlwZT0iTVNBcnRib2FyZEdyb3VwIiBmaWxsPSIjQzhEN0UxIj4gICAgICAgICAgICA8cGF0aCBkPSJNMTUuNSw2IEwxNyw3LjUgTDEwLjI1LDE0LjI1IEwzLjUsNy41IEw1LDYgTDEwLjI1LDExLjI1IEwxNS41LDYgWiIgaWQ9IkRvd24tQXJyb3ciIHNrZXRjaDp0eXBlPSJNU1NoYXBlR3JvdXAiPjwvcGF0aD4gICAgICAgIDwvZz4gICAgPC9nPjwvc3ZnPg==) no-repeat right 10px center;
	border-color: #c8d7e1;
	border-style: solid;
	border-radius: 4px;
	border-width: 1px 1px 2px;
	color: #2e4453;
	cursor: pointer;
	display: inline-block;
	margin: 0;
	outline: 0;
	overflow: hidden;
	font-size: 14px;
	line-height: 21px;
	font-weight: 600;
	text-overflow: ellipsis;
	text-decoration: none;
	vertical-align: top;
	white-space: nowrap;
	box-sizing: border-box;

	/* Aligns the text to the 8px baseline grid and adds padding on right to allow for the arrow. */
	padding: 7px 32px 9px 14px;
	-webkit-appearance: none;
	-moz-appearance: none;
	appearance: none;
}

select:hover {
	background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiIHN0YW5kYWxvbmU9Im5vIj8+PHN2ZyB3aWR0aD0iMjBweCIgaGVpZ2h0PSIyMHB4IiB2aWV3Qm94PSIwIDAgMjAgMjAiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayIgeG1sbnM6c2tldGNoPSJodHRwOi8vd3d3LmJvaGVtaWFuY29kaW5nLmNvbS9za2V0Y2gvbnMiPiAgICAgICAgPHRpdGxlPmFycm93LWRvd248L3RpdGxlPiAgICA8ZGVzYz5DcmVhdGVkIHdpdGggU2tldGNoLjwvZGVzYz4gICAgPGRlZnM+PC9kZWZzPiAgICA8ZyBpZD0iUGFnZS0xIiBzdHJva2U9Im5vbmUiIHN0cm9rZS13aWR0aD0iMSIgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIiBza2V0Y2g6dHlwZT0iTVNQYWdlIj4gICAgICAgIDxnIGlkPSJhcnJvdy1kb3duIiBza2V0Y2g6dHlwZT0iTVNBcnRib2FyZEdyb3VwIiBmaWxsPSIjYThiZWNlIj4gICAgICAgICAgICA8cGF0aCBkPSJNMTUuNSw2IEwxNyw3LjUgTDEwLjI1LDE0LjI1IEwzLjUsNy41IEw1LDYgTDEwLjI1LDExLjI1IEwxNS41LDYgWiIgaWQ9IkRvd24tQXJyb3ciIHNrZXRjaDp0eXBlPSJNU1NoYXBlR3JvdXAiPjwvcGF0aD4gICAgICAgIDwvZz4gICAgPC9nPjwvc3ZnPg==);
}

select:focus {
	background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiIHN0YW5kYWxvbmU9Im5vIj8+PHN2ZyB3aWR0aD0iMjBweCIgaGVpZ2h0PSIyMHB4IiB2aWV3Qm94PSIwIDAgMjAgMjAiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayIgeG1sbnM6c2tldGNoPSJodHRwOi8vd3d3LmJvaGVtaWFuY29kaW5nLmNvbS9za2V0Y2gvbnMiPiA8dGl0bGU+YXJyb3ctZG93bjwvdGl0bGU+IDxkZXNjPkNyZWF0ZWQgd2l0aCBTa2V0Y2guPC9kZXNjPiA8ZGVmcz48L2RlZnM+IDxnIGlkPSJQYWdlLTEiIHN0cm9rZT0ibm9uZSIgc3Ryb2tlLXdpZHRoPSIxIiBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiIHNrZXRjaDp0eXBlPSJNU1BhZ2UiPiA8ZyBpZD0iYXJyb3ctZG93biIgc2tldGNoOnR5cGU9Ik1TQXJ0Ym9hcmRHcm91cCIgZmlsbD0iIzJlNDQ1MyI+IDxwYXRoIGQ9Ik0xNS41LDYgTDE3LDcuNSBMMTAuMjUsMTQuMjUgTDMuNSw3LjUgTDUsNiBMMTAuMjUsMTEuMjUgTDE1LjUsNiBaIiBpZD0iRG93bi1BcnJvdyIgc2tldGNoOnR5cGU9Ik1TU2hhcGVHcm91cCI+PC9wYXRoPiA8L2c+IDwvZz48L3N2Zz4=);
	border-color: #00aadc;
	box-shadow: 0 0 0 2px #78dcfa;
	outline: 0;
	-moz-outline:none;
	-moz-user-focus:ignore;
}

select:disabled,
select:hover:disabled {
	background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiIHN0YW5kYWxvbmU9Im5vIj8+PHN2ZyB3aWR0aD0iMjBweCIgaGVpZ2h0PSIyMHB4IiB2aWV3Qm94PSIwIDAgMjAgMjAiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayIgeG1sbnM6c2tldGNoPSJodHRwOi8vd3d3LmJvaGVtaWFuY29kaW5nLmNvbS9za2V0Y2gvbnMiPiAgICAgICAgPHRpdGxlPmFycm93LWRvd248L3RpdGxlPiAgICA8ZGVzYz5DcmVhdGVkIHdpdGggU2tldGNoLjwvZGVzYz4gICAgPGRlZnM+PC9kZWZzPiAgICA8ZyBpZD0iUGFnZS0xIiBzdHJva2U9Im5vbmUiIHN0cm9rZS13aWR0aD0iMSIgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIiBza2V0Y2g6dHlwZT0iTVNQYWdlIj4gICAgICAgIDxnIGlkPSJhcnJvdy1kb3duIiBza2V0Y2g6dHlwZT0iTVNBcnRib2FyZEdyb3VwIiBmaWxsPSIjZTllZmYzIj4gICAgICAgICAgICA8cGF0aCBkPSJNMTUuNSw2IEwxNyw3LjUgTDEwLjI1LDE0LjI1IEwzLjUsNy41IEw1LDYgTDEwLjI1LDExLjI1IEwxNS41LDYgWiIgaWQ9IkRvd24tQXJyb3ciIHNrZXRjaDp0eXBlPSJNU1NoYXBlR3JvdXAiPjwvcGF0aD4gICAgICAgIDwvZz4gICAgPC9nPjwvc3ZnPg==) no-repeat right 10px center;;
}

select.is-compact {
	min-width: 0;
	padding: 0 20px 2px 6px;
	margin: 0 4px;
	background-position:  right 5px center;
	background-size: 12px 12px;
}

/* Make it display:block when it follows a label */
label select,
label + select {
	display: block;
	min-width: 200px;
}

label select.is-compact,
label + select.is-compact {
	display: inline-block;
	min-width: 0;
}

/* IE: Remove the default arrow */
select::-ms-expand {
	display: none;
}

/* IE: Remove default background and color styles on focus */
select::-ms-value {
	background: none;
	color: #2e4453;
}

/* Firefox: Remove the focus outline, see http://stackoverflow.com/questions/3773430/remove-outline-from-select-box-in-ff/18853002#18853002 */
select:-moz-focusring {
	color: transparent;
	text-shadow: 0 0 0 #2e4453;
}


/* ==========================================================================
** Buttons
** ======================================================================== */

input[type="submit"] {
	background: #fff;
	border-color: #c8d7e1;
	border-style: solid;
	border-width: 1px 1px 2px;
	color: #2e4453;
	cursor: pointer;
	display: inline-block;
	margin: 24px 0 0;
	outline: 0;
	overflow: hidden;
	font-weight: 500;
	text-overflow: ellipsis;
	text-decoration: none;
	vertical-align: top;
	box-sizing: border-box;
	font-size: 14px;
	line-height: 21px;
	border-radius: 4px;
	padding: 7px 14px 9px;
	-webkit-appearance: none;
	-moz-appearance: none;
			 appearance: none;
}

input[type="submit"]:hover {
	border-color: #a8bece;
	color: #2e4453;
}

input[type="submit"]:active {
	border-width: 2px 1px 1px;
}

input[type="submit"]:visited {
	color: #2e4453;
}

input[type="submit"][disabled],
input[type="submit"]:disabled {
	color: #e9eff3;
	background: #fff;
	border-color: #e9eff3;
	cursor: default;
}

input[type="submit"][disabled]:active,
input[type="submit"]:disabled:active {
	border-width: 1px 1px 2px;
}

input[type="submit"]:focus {
	border-color: #00aadc;
	box-shadow: 0 0 0 2px #78dcfa;
}

/* ==========================================================================
** Preview styles
** ======================================================================== */

.wpview.wpview-wrap[data-wpview-type="contact-form"] iframe.inline-edit-contact-form {
	width: 100%;
	min-height: 500px;
	border: 0;
	overflow: hidden;
	margin-bottom: 0;
	display: block;
}

.contact-submit.contact-submit {
	margin-top: 0;
	margin-bottom: 0;
}

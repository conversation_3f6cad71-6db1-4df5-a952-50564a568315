<?php
/**
 * <PERSON><PERSON><PERSON> to create Travel Package 2: South African Luxury Experience
 * This script creates a new Advanced Product post for the luxury travel package
 */

// Include WordPress
require_once('wp-config.php');
require_once('wp-load.php');

// Package 2 Data
$package_data = array(
    'title' => 'South African Luxury Experience',
    'duration' => '12 Days',
    'price_adult' => 'Contact for Pricing',
    'price_child' => 'Contact for Pricing',
    'accommodation_level' => '5*',
    'starting_location' => 'Dubai',
    'ending_location' => 'Dubai',
    'highlights' => array(
        'Luxury 5-star accommodations throughout',
        '<PERSON>\'s Balloon Safari',
        'VIP conservation talks with rangers',
        'Private yacht experiences in Cape Town',
        'Exclusive wine blending experiences',
        'Gourmet dining and culinary tours'
    ),
    'itinerary' => array(
        array(
            'day' => 1,
            'location' => 'Dubai to Johannesburg',
            'accommodation' => 'Sandton Luxury City Hotel (5*)',
            'activities' => 'Emirates flight: Dubai to Johannesburg',
            'tip' => 'Ensure a private airport transfer for seamless arrival & check-in'
        ),
        array(
            'day' => 2,
            'location' => 'Johannesburg',
            'accommodation' => 'Sandton Luxury City Hotel',
            'activities' => '<PERSON>\'s Balloon Safari, Farm-to-Fork Experience in Muldersdrift, Long Table Lunch',
            'tip' => 'Opt for a sunset mead tasting for a unique, locally inspired pairing'
        ),
        array(
            'day' => 3,
            'location' => 'Kruger Area',
            'accommodation' => 'Tingana Collection Lodge',
            'activities' => 'VIP Meet-and-Greet with Ranger & Conservation Talk, Transfer to Lodge, Sunset Safari',
            'tip' => 'Kick off the safari experience with a conservation briefing for deeper insight'
        ),
        array(
            'day' => 4,
            'location' => 'Kruger Area',
            'accommodation' => 'Tingana Collection Lodge',
            'activities' => 'Full-day safari, bush breakfast, wildlife dining',
            'tip' => 'Book a guided photography safari for expert wildlife shots'
        ),
        array(
            'day' => 5,
            'location' => 'Cape Town',
            'accommodation' => 'Cape Town Iconic Hotel (5*)',
            'activities' => 'Flight to Cape Town, sunset yacht experience',
            'tip' => 'Upgrade to oyster pairing on the yacht for a luxe touch'
        ),
        array(
            'day' => 6,
            'location' => 'Cape Town',
            'accommodation' => 'Cape Town Iconic Hotel',
            'activities' => 'Pink Combi Culinary Tour, Muizenberg Surfing Lessons, Classic Car Adventures, Award-Winning Seafood Dinner',
            'tip' => 'Experience the vibrant local culture through authentic culinary experiences'
        ),
        array(
            'day' => 7,
            'location' => 'Cape Town',
            'accommodation' => 'Cape Town Iconic Hotel',
            'activities' => 'Noordhoek Private Yacht Sundowner, Seafood Foraging in Scarborough, Kelp Tasting Along the Atlantic, Indigenous Flavors',
            'tip' => 'Curated tasting of fynbos, buchu, local spices, wild botanical'
        ),
        array(
            'day' => 8,
            'location' => 'Cape Winelands',
            'accommodation' => 'Stellenbosch Boutique Accommodation',
            'activities' => 'Boutique vineyard tour and blending experience',
            'tip' => 'Book a private barrel-room tasting for exclusive insights'
        ),
        array(
            'day' => 9,
            'location' => 'Cape Winelands to Knysna',
            'accommodation' => 'Stellenbosch Boutique Accommodation',
            'activities' => 'Scenic drive and wine estate visits',
            'tip' => 'Extend the experience with a custom chocolate-making session'
        ),
        array(
            'day' => 10,
            'location' => 'Knysna',
            'accommodation' => 'Luxury Lifestyle Hotel',
            'activities' => 'Lagoon-Side Dining, Cango Caves Exploration',
            'tip' => 'Consider a private guided oyster tasting on the Knysna lagoon'
        ),
        array(
            'day' => 11,
            'location' => 'Garden Route',
            'accommodation' => 'Luxury Lifestyle Hotel',
            'activities' => 'Ostrich Farm Experience, Wildlife Encounters at Elephant Sanctuary, Tsitsikamma Nature Excursion',
            'tip' => 'Opt for an extended hike through Tsitsikamma with a local guide for hidden gems'
        ),
        array(
            'day' => 12,
            'location' => 'Cape Town/Dubai',
            'accommodation' => 'N/A',
            'activities' => 'Final SA luxury dining experience, flight to Dubai',
            'tip' => 'Consider a farewell dinner at FYN for a modern SA culinary finale'
        )
    ),
    'inclusions' => array(
        '11 nights luxury accommodation',
        'All meals as specified',
        'Private transfers throughout',
        'Domestic flights',
        'All tours & activities mentioned',
        'Professional guide services',
        'VIP experiences and exclusive access'
    )
);

// Create the post content
$content = '<h2>Overview</h2>';
$content .= '<p>Indulge in the ultimate South African luxury experience with this meticulously crafted 12-day journey. From hot air balloon safaris over the Magaliesberg to private yacht experiences in Cape Town, every moment is designed to exceed expectations. Stay in the finest 5-star accommodations while exploring South Africa\'s most iconic destinations with exclusive access and personalized service.</p>';

$content .= '<h2>Tour Highlights</h2>';
$content .= '<ul>';
foreach ($package_data['highlights'] as $highlight) {
    $content .= '<li>' . $highlight . '</li>';
}
$content .= '</ul>';

$content .= '<h2>Detailed Itinerary</h2>';
foreach ($package_data['itinerary'] as $day) {
    $content .= '<h3>Day ' . $day['day'] . ': ' . $day['location'] . '</h3>';
    $content .= '<p><strong>Accommodation:</strong> ' . $day['accommodation'] . '</p>';
    $content .= '<p><strong>Activities:</strong> ' . $day['activities'] . '</p>';
    $content .= '<p><strong>Tingana Tip:</strong> ' . $day['tip'] . '</p>';
}

$content .= '<h2>What\'s Included</h2>';
$content .= '<ul>';
foreach ($package_data['inclusions'] as $inclusion) {
    $content .= '<li>' . $inclusion . '</li>';
}
$content .= '</ul>';

$content .= '<h2>Pricing</h2>';
$content .= '<p><strong>Contact us for exclusive pricing</strong></p>';
$content .= '<p><em>This luxury experience is tailored to your preferences with premium accommodations and exclusive access.</em></p>';

// Create the post
$post_data = array(
    'post_title' => $package_data['title'],
    'post_content' => $content,
    'post_status' => 'publish',
    'post_type' => 'ap_product',
    'post_author' => 1,
    'meta_input' => array(
        'ap_price' => '0',
        'ap_price_contact' => 'yes',
        'ap_product_type' => 'luxury_tour',
        'duration' => $package_data['duration'],
        'accommodation_level' => $package_data['accommodation_level'],
        'starting_location' => $package_data['starting_location'],
        'ending_location' => $package_data['ending_location']
    )
);

$post_id = wp_insert_post($post_data);

if ($post_id) {
    echo "Successfully created travel package: " . $package_data['title'] . " (ID: $post_id)\n";
    
    // Add to product categories
    $tour_term = get_term_by('name', 'Tours', 'ap_product_cat');
    $luxury_term = get_term_by('name', 'Luxury Tours', 'ap_product_cat');
    
    if (!$tour_term) {
        $tour_term = wp_insert_term('Tours', 'ap_product_cat');
        if (!is_wp_error($tour_term)) {
            $tour_term_id = $tour_term['term_id'];
        }
    } else {
        $tour_term_id = $tour_term->term_id;
    }
    
    if (!$luxury_term) {
        $luxury_term = wp_insert_term('Luxury Tours', 'ap_product_cat');
        if (!is_wp_error($luxury_term)) {
            $luxury_term_id = $luxury_term['term_id'];
        }
    } else {
        $luxury_term_id = $luxury_term->term_id;
    }
    
    $term_ids = array();
    if (isset($tour_term_id)) $term_ids[] = $tour_term_id;
    if (isset($luxury_term_id)) $term_ids[] = $luxury_term_id;
    
    if (!empty($term_ids)) {
        wp_set_post_terms($post_id, $term_ids, 'ap_product_cat');
        echo "Added to Tours and Luxury Tours categories\n";
    }
} else {
    echo "Failed to create travel package\n";
}
?>

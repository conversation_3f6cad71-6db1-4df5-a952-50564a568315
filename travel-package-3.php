<?php
/**
 * <PERSON><PERSON><PERSON> to create Travel Package 3: South African Classic Tour
 * This script creates a new Advanced Product post for the classic travel package
 */

// Include WordPress
require_once('wp-config.php');
require_once('wp-load.php');

// Package 3 Data
$package_data = array(
    'title' => 'South African Classic Tour',
    'duration' => '9 Days',
    'price_adult' => '$1950',
    'price_child' => '$1550',
    'accommodation_level' => '4*',
    'starting_location' => 'Johannesburg',
    'ending_location' => 'Cape Town',
    'highlights' => array(
        'Kruger National Park safari adventure',
        'Panorama Route scenic drive',
        'Cape Peninsula coastal tour',
        'Table Mountain aerial cableway',
        'Cape Winelands wine tasting',
        'V&A Waterfront shopping and dining'
    ),
    'itinerary' => array(
        array(
            'day' => 1,
            'location' => 'Johannesburg',
            'accommodation' => '4* Hotel',
            'activities' => 'Arrival in Johannesburg – Meet & Greet, Transfer to Hotel',
            'tip' => 'Rest and prepare for your South African adventure'
        ),
        array(
            'day' => 2,
            'location' => 'Mpumalanga',
            'accommodation' => 'Buckler\'s Africa Lodge',
            'activities' => 'Road trip from Johannesburg to Mpumalanga (+-6h40min) with scenic stops for photo opportunities (B,L,D)',
            'tip' => 'Opt for an early morning game drive—predators are most active then. Try silent tracking for spotting elusive wildlife.'
        ),
        array(
            'day' => 3,
            'location' => 'Mpumalanga',
            'accommodation' => 'Buckler\'s Africa Lodge',
            'activities' => 'Safari Adventure Day in the Kruger National Park (B,L,D)',
            'tip' => 'Keep your camera ready for the Big Five encounters'
        ),
        array(
            'day' => 4,
            'location' => 'Mpumalanga',
            'accommodation' => 'Buckler\'s Africa Lodge',
            'activities' => 'Sight-Seeing day along the Panorama route (B,L,D)',
            'tip' => 'Enjoy a picnic lunch at Bourke\'s Luck Potholes, pairing it with locally brewed South African rooibos tea.'
        ),
        array(
            'day' => 5,
            'location' => 'Cape Town',
            'accommodation' => '4* Hotel',
            'activities' => 'Fly from Mpumalanga to Cape Town (+-2h45min) (B,L), Transfer and check in',
            'tip' => 'Request a sea-facing room—waking up to Table Mountain or the Atlantic Ocean is an unbeatable experience.'
        ),
        array(
            'day' => 6,
            'location' => 'Cape Town',
            'accommodation' => '4* Hotel',
            'activities' => 'Cape Peninsula Tour – scenic coastal sightseeing (B,L)',
            'tip' => 'Take a detour to Kalk Bay—a fishing village with ocean-view cafés and some of the best fish and chips in Cape Town.'
        ),
        array(
            'day' => 7,
            'location' => 'Cape Town',
            'accommodation' => '4* Hotel',
            'activities' => 'Table Mountain and Aerial Cableway Tour',
            'tip' => 'Visit Table Mountain at sunrise or late afternoon for stunning golden light. Hike Platteklip Gorge before taking the cableway down.'
        ),
        array(
            'day' => 8,
            'location' => 'Cape Town',
            'accommodation' => '4* Hotel',
            'activities' => 'Winelands Tour of the vineyards with lunch (B,L)',
            'tip' => 'Visit Babylonstoren for a sensory garden walk before wine tasting—it enhances the flavors beautifully. Non-Alcoholic Options available.'
        ),
        array(
            'day' => 9,
            'location' => 'Cape Town',
            'accommodation' => 'N/A',
            'activities' => 'Leisure day to relax and shop at your own pace, Evening Transfer to airport for departure flight',
            'tip' => 'Explore The Watershed at V&A Waterfront for unique, locally-crafted souvenirs that capture South Africa\'s essence.'
        )
    ),
    'inclusions' => array(
        '8 nights accommodation in 4* hotels',
        'Breakfast daily',
        'Lunches as specified',
        'Dinners as specified',
        'All transfers and transportation',
        'Domestic flight from Mpumalanga to Cape Town',
        'Professional guide services',
        'All tours and activities mentioned'
    )
);

// Create the post content
$content = '<h2>Overview</h2>';
$content .= '<p>Discover the essence of South Africa on this classic 9-day tour that showcases the country\'s most iconic destinations. From the wildlife-rich Kruger National Park to the stunning landscapes of the Cape Peninsula, this carefully crafted itinerary offers the perfect introduction to South Africa\'s natural beauty, rich culture, and warm hospitality.</p>';

$content .= '<h2>Tour Highlights</h2>';
$content .= '<ul>';
foreach ($package_data['highlights'] as $highlight) {
    $content .= '<li>' . $highlight . '</li>';
}
$content .= '</ul>';

$content .= '<h2>Detailed Itinerary</h2>';
foreach ($package_data['itinerary'] as $day) {
    $content .= '<h3>Day ' . $day['day'] . ': ' . $day['location'] . '</h3>';
    $content .= '<p><strong>Accommodation:</strong> ' . $day['accommodation'] . '</p>';
    $content .= '<p><strong>Activities:</strong> ' . $day['activities'] . '</p>';
    $content .= '<p><strong>Tingana Tip:</strong> ' . $day['tip'] . '</p>';
}

$content .= '<h2>What\'s Included</h2>';
$content .= '<ul>';
foreach ($package_data['inclusions'] as $inclusion) {
    $content .= '<li>' . $inclusion . '</li>';
}
$content .= '</ul>';

$content .= '<h2>Meal Plan</h2>';
$content .= '<p><strong>B</strong> = Breakfast | <strong>L</strong> = Lunch | <strong>D</strong> = Dinner</p>';

$content .= '<h2>Pricing</h2>';
$content .= '<p><strong>From ' . $package_data['price_adult'] . ' per adult | ' . $package_data['price_child'] . ' per child</strong></p>';
$content .= '<p><em>Prices are per person based on double occupancy</em></p>';

// Create the post
$post_data = array(
    'post_title' => $package_data['title'],
    'post_content' => $content,
    'post_status' => 'publish',
    'post_type' => 'ap_product',
    'post_author' => 1,
    'meta_input' => array(
        'ap_price' => '1950',
        'ap_product_type' => 'tour',
        'duration' => $package_data['duration'],
        'price_adult' => $package_data['price_adult'],
        'price_child' => $package_data['price_child'],
        'accommodation_level' => $package_data['accommodation_level'],
        'starting_location' => $package_data['starting_location'],
        'ending_location' => $package_data['ending_location']
    )
);

$post_id = wp_insert_post($post_data);

if ($post_id) {
    echo "Successfully created travel package: " . $package_data['title'] . " (ID: $post_id)\n";
    
    // Add to product categories
    $tour_term = get_term_by('name', 'Tours', 'ap_product_cat');
    $classic_term = get_term_by('name', 'Classic Tours', 'ap_product_cat');
    
    if (!$tour_term) {
        $tour_term = wp_insert_term('Tours', 'ap_product_cat');
        if (!is_wp_error($tour_term)) {
            $tour_term_id = $tour_term['term_id'];
        }
    } else {
        $tour_term_id = $tour_term->term_id;
    }
    
    if (!$classic_term) {
        $classic_term = wp_insert_term('Classic Tours', 'ap_product_cat');
        if (!is_wp_error($classic_term)) {
            $classic_term_id = $classic_term['term_id'];
        }
    } else {
        $classic_term_id = $classic_term->term_id;
    }
    
    $term_ids = array();
    if (isset($tour_term_id)) $term_ids[] = $tour_term_id;
    if (isset($classic_term_id)) $term_ids[] = $classic_term_id;
    
    if (!empty($term_ids)) {
        wp_set_post_terms($post_id, $term_ids, 'ap_product_cat');
        echo "Added to Tours and Classic Tours categories\n";
    }
} else {
    echo "Failed to create travel package\n";
}
?>

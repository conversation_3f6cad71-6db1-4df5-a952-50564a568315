/* ==========================================================================
** Normalize
** ======================================================================== */

html {
	direction: ltr;
}

/* Links */
a,
a:visited {
	color: #0087be;
	text-decoration: none;
}

/* ==========================================================================
** Card
** ======================================================================= */

.card,
body {
	display: block;
	position: relative;
	margin: 0 auto 10px auto;
	padding: 16px;
	box-sizing: border-box;
	background: #fff;
	box-shadow: 0 0 0 1px rgba(200, 215, 225, 0.5), 0 1px 2px #e9eff3;
}

body {
	margin: 0;
	background: #f6f7f7;
}

.card::after {
	content: ".";
	display: block;
	height: 0;
	clear: both;
	visibility: hidden;
}

.card:hover,
.card:focus {
	box-shadow: 0 0 0 1px #999, 0 1px 2px #e9eff3;
}

.card .delete-field {
	display: block;
	float: right;
}

@media ( min-width: 481px ) {

	.card {
		margin-bottom: 16px;
		padding: 24px;
	}

	body {
		padding: 24px;
	}
}

.card.is-compact {
	margin-bottom: 1px;
}

@media ( min-width: 481px ) {

	.card.is-compact {
		margin-bottom: 1px;
		padding: 16px 24px;
	}
}

.card > div {
	margin-top: 24px;
}

.card > div:first-child {
	margin-top: 0;
}


/* ==========================================================================
** Labels
** ======================================================================= */

label {
	display: block;
	font-size: 14px;
	font-weight: 600;
	margin-bottom: 5px;
	margin-top: 8px;
}

label:first-of-type {
	margin-top: 4px;
}


/* ==========================================================================
** Text Inputs
** ======================================================================= */

input[type="text"],
input[type="tel"],
input[type="email"],
input[type="url"] {
	border-radius: 0;
	appearance: none;
	box-sizing: border-box;
	margin: 0;
	padding: 7px 14px;
	width: 100%;
	color: #2e4453;
	font-size: 16px;
	line-height: 1.5;
	border: 1px solid #c8d7e1;
	background-color: #fff;
	transition: all .15s ease-in-out;
	box-shadow: none;
}

input[type="text"]::placeholder,
input[type="tel"]::placeholder,
input[type="email"]::placeholder,
input[type="url"]::placeholder {
	color: #87a6bc;
}

input[type="text"]:hover,
input[type="tel"]:hover,
input[type="email"]:hover,
input[type="url"]:hover {
	border-color: #a8bece;
}

input[type="text"]:focus,
input[type="tel"]:focus,
input[type="email"]:focus,
input[type="url"]:focus {
	border-color: #0087be;
	outline: none;
	box-shadow: 0 0 0 2px #78dcfa;
}

input[type="text"]:focus::-ms-clear,
input[type="tel"]:focus::-ms-clear,
input[type="email"]:focus::-ms-clear,
input[type="url"]:focus::-ms-clear {
	display: none;
}

input[type="text"]:disabled,
input[type="tel"]:disabled,
input[type="email"]:disabled,
input[type="url"]:disabled {
	background: #f3f6f8;
	border-color: #e9eff3;
	color: #a8bece;
	-webkit-text-fill-color: #a8bece;
}

input[type="text"]:disabled:hover,
input[type="tel"]:disabled:hover,
input[type="email"]:disabled:hover,
input[type="url"]:disabled:hover {
	cursor: default;
}

input[type="text"]:disabled::placeholder,
input[type="tel"]:disabled::placeholder,
input[type="email"]:disabled::placeholder,
input[type="url"]:disabled::placeholder {
	color: #a8bece;
}


/* ==========================================================================
** Textareas
** ======================================================================= */

textarea {
	border-radius: 0;
	appearance: none;
	box-sizing: border-box;
	margin: 0;
	padding: 7px 14px;
	height: 92px;
	width: 100%;
	color: #2e4453;
	font-size: 16px;
	line-height: 1.5;
	border: 1px solid #c8d7e1;
	background-color: #fff;
	transition: all .15s ease-in-out;
	box-shadow: none;
}

textarea::placeholder {
	color: #87a6bc;
}

textarea:hover {
	border-color: #a8bece;
}

textarea:focus {
	border-color: #0087be;
	outline: none;
	box-shadow: 0 0 0 2px #78dcfa;
}

textarea:focus::-ms-clear {
	display: none;
}

textarea:disabled {
	background: #f3f6f8;
	border-color: #e9eff3;
	color: #a8bece;
	-webkit-text-fill-color: #a8bece;
}

textarea:disabled:hover {
	cursor: default;
}

textarea:disabled::placeholder {
	color: #a8bece;
}


/* ==========================================================================
** Checkboxes
** ======================================================================= */

.checkbox,
input[type="checkbox"] {
	-webkit-appearance: none;
	display: inline-block;
	box-sizing: border-box;
	margin: 2px 0 0;
	width: 16px;
	height: 16px;
	float: left;
	outline: 0;
	padding: 0;
	box-shadow: none;
	background-color: #fff;
	border: 1px solid #c8d7e1;
	color: #2e4453;
	font-size: 16px;
	line-height: 0;
	text-align: center;
	vertical-align: middle;
	appearance: none;
	transition: all .15s ease-in-out;
	clear: none;
	cursor: pointer;
}

.checkbox:checked::before,
input[type="checkbox"]:checked::before {
	content: '\f147';
	font-family: dashicons;
	margin: -3px 0 0 -4px;
	float: left;
	display: inline-block;
	vertical-align: middle;
	width: 16px;
	font-size: 20px;
	line-height: 1;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	speak: none;
	color: #00aadc;
}

.checkbox:disabled:checked::before,
input[type="checkbox"]:disabled:checked::before {
	color: #a8bece;
}

.checkbox:hover,
input[type="checkbox"]:hover {
	border-color: #a8bece;
}

.checkbox:focus,
input[type="checkbox"]:focus {
	border-color: #0087be;
	outline: none;
	box-shadow: 0 0 0 2px #78dcfa;
}

.checkbox:disabled,
input[type="checkbox"]:disabled {
	background: #f3f6f8;
	border-color: #e9eff3;
	color: #a8bece;
	opacity: 1;
}

.checkbox:disabled:hover,
input[type="checkbox"]:disabled:hover {
	cursor: default;
}

.checkbox + span,
input[type="checkbox"] + span {
	display: block;
	font-weight: 400;
	margin-left: 24px;
}


/* ==========================================================================
** Radio buttons
** ======================================================================== */

.radio-button,
input[type="radio"] {
	color: #2e4453;
	font-size: 16px;
	border: 1px solid #c8d7e1;
	background-color: #fff;
	transition: all .15s ease-in-out;
	box-sizing: border-box;
	-webkit-appearance: none;
	clear: none;
	cursor: pointer;
	display: inline-block;
	height: 16px;
	margin: 2px 4px 0 0;
	float: left;
	outline: 0;
	padding: 0;
	text-align: center;
	vertical-align: middle;
	width: 16px;
	min-width: 16px;
	appearance: none;
	border-radius: 50%;
	line-height: 10px;
}

.radio-button:hover,
input[type="radio"]:hover {
	border-color: #a8bece;
}

.radio-button:focus,
input[type="radio"]:focus {
	border-color: #0087be;
	outline: none;
	box-shadow: 0 0 0 2px #78dcfa;
}

.radio-button:focus::-ms-clear,
input[type="radio"]:focus::-ms-clear {
	display: none;
}

.radio-button:checked::before,
input[type="radio"]:checked::before {
	float: left;
	display: inline-block;
	content: '\2022';
	margin: 3px;
	width: 8px;
	height: 8px;
	text-indent: -9999px;
	background: #00aadc;
	vertical-align: middle;
	border-radius: 50%;
	animation: grow .2s ease-in-out;
}

.radio-button:disabled,
input[type="radio"]:disabled {
	background: #f3f6f8;
	border-color: #e9eff3;
	color: #a8bece;
	opacity: 1;
	-webkit-text-fill-color: #a8bece;
}

.radio-button:disabled:hover,
input[type="radio"]:disabled:hover {
	cursor: default;
}

.radio-button:disabled::placeholder,
input[type="radio"]:disabled::placeholder {
	color: #a8bece;
}

.radio-button:disabled:checked::before,
input[type="radio"]:disabled:checked::before {
	background: #e9eff3;
}

.radio-button + span,
input[type="radio"] + span {
	display: block;
	font-weight: 400;
	margin-left: 24px;
}

@keyframes grow {

	0% {
		transform: scale(0.3);
	}

	60% {
		transform: scale(1.15);
	}

	100% {
		transform: scale(1);
	}
}

@keyframes grow {

	0% {
		transform: scale(0.3);
	}

	60% {
		transform: scale(1.15);
	}

	100% {
		transform: scale(1);
	}
}


/* ==========================================================================
** Selects
** ======================================================================== */

select {
	background: #fff url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiIHN0YW5kYWxvbmU9Im5vIj8+PHN2ZyB3aWR0aD0iMjBweCIgaGVpZ2h0PSIyMHB4IiB2aWV3Qm94PSIwIDAgMjAgMjAiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayIgeG1sbnM6c2tldGNoPSJodHRwOi8vd3d3LmJvaGVtaWFuY29kaW5nLmNvbS9za2V0Y2gvbnMiPiAgICAgICAgPHRpdGxlPmFycm93LWRvd248L3RpdGxlPiAgICA8ZGVzYz5DcmVhdGVkIHdpdGggU2tldGNoLjwvZGVzYz4gICAgPGRlZnM+PC9kZWZzPiAgICA8ZyBpZD0iUGFnZS0xIiBzdHJva2U9Im5vbmUiIHN0cm9rZS13aWR0aD0iMSIgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIiBza2V0Y2g6dHlwZT0iTVNQYWdlIj4gICAgICAgIDxnIGlkPSJhcnJvdy1kb3duIiBza2V0Y2g6dHlwZT0iTVNBcnRib2FyZEdyb3VwIiBmaWxsPSIjQzhEN0UxIj4gICAgICAgICAgICA8cGF0aCBkPSJNMTUuNSw2IEwxNyw3LjUgTDEwLjI1LDE0LjI1IEwzLjUsNy41IEw1LDYgTDEwLjI1LDExLjI1IEwxNS41LDYgWiIgaWQ9IkRvd24tQXJyb3ciIHNrZXRjaDp0eXBlPSJNU1NoYXBlR3JvdXAiPjwvcGF0aD4gICAgICAgIDwvZz4gICAgPC9nPjwvc3ZnPg==) no-repeat right 10px center;
	border-color: #c8d7e1;
	border-style: solid;
	border-radius: 4px;
	border-width: 1px 1px 2px;
	color: #2e4453;
	cursor: pointer;
	display: inline-block;
	margin: 0;
	outline: 0;
	overflow: hidden;
	font-size: 14px;
	line-height: 21px;
	font-weight: 600;
	text-overflow: ellipsis;
	text-decoration: none;
	vertical-align: top;
	white-space: nowrap;
	box-sizing: border-box;

	/* Aligns the text to the 8px baseline grid and adds padding on right to allow for the arrow. */
	padding: 7px 32px 9px 14px;
	-webkit-appearance: none;
	-moz-appearance: none;
	appearance: none;
}

select:hover {
	background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiIHN0YW5kYWxvbmU9Im5vIj8+PHN2ZyB3aWR0aD0iMjBweCIgaGVpZ2h0PSIyMHB4IiB2aWV3Qm94PSIwIDAgMjAgMjAiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayIgeG1sbnM6c2tldGNoPSJodHRwOi8vd3d3LmJvaGVtaWFuY29kaW5nLmNvbS9za2V0Y2gvbnMiPiAgICAgICAgPHRpdGxlPmFycm93LWRvd248L3RpdGxlPiAgICA8ZGVzYz5DcmVhdGVkIHdpdGggU2tldGNoLjwvZGVzYz4gICAgPGRlZnM+PC9kZWZzPiAgICA8ZyBpZD0iUGFnZS0xIiBzdHJva2U9Im5vbmUiIHN0cm9rZS13aWR0aD0iMSIgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIiBza2V0Y2g6dHlwZT0iTVNQYWdlIj4gICAgICAgIDxnIGlkPSJhcnJvdy1kb3duIiBza2V0Y2g6dHlwZT0iTVNBcnRib2FyZEdyb3VwIiBmaWxsPSIjYThiZWNlIj4gICAgICAgICAgICA8cGF0aCBkPSJNMTUuNSw2IEwxNyw3LjUgTDEwLjI1LDE0LjI1IEwzLjUsNy41IEw1LDYgTDEwLjI1LDExLjI1IEwxNS41LDYgWiIgaWQ9IkRvd24tQXJyb3ciIHNrZXRjaDp0eXBlPSJNU1NoYXBlR3JvdXAiPjwvcGF0aD4gICAgICAgIDwvZz4gICAgPC9nPjwvc3ZnPg==);
}

select:focus {
	background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiIHN0YW5kYWxvbmU9Im5vIj8+PHN2ZyB3aWR0aD0iMjBweCIgaGVpZ2h0PSIyMHB4IiB2aWV3Qm94PSIwIDAgMjAgMjAiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayIgeG1sbnM6c2tldGNoPSJodHRwOi8vd3d3LmJvaGVtaWFuY29kaW5nLmNvbS9za2V0Y2gvbnMiPiA8dGl0bGU+YXJyb3ctZG93bjwvdGl0bGU+IDxkZXNjPkNyZWF0ZWQgd2l0aCBTa2V0Y2guPC9kZXNjPiA8ZGVmcz48L2RlZnM+IDxnIGlkPSJQYWdlLTEiIHN0cm9rZT0ibm9uZSIgc3Ryb2tlLXdpZHRoPSIxIiBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiIHNrZXRjaDp0eXBlPSJNU1BhZ2UiPiA8ZyBpZD0iYXJyb3ctZG93biIgc2tldGNoOnR5cGU9Ik1TQXJ0Ym9hcmRHcm91cCIgZmlsbD0iIzJlNDQ1MyI+IDxwYXRoIGQ9Ik0xNS41LDYgTDE3LDcuNSBMMTAuMjUsMTQuMjUgTDMuNSw3LjUgTDUsNiBMMTAuMjUsMTEuMjUgTDE1LjUsNiBaIiBpZD0iRG93bi1BcnJvdyIgc2tldGNoOnR5cGU9Ik1TU2hhcGVHcm91cCI+PC9wYXRoPiA8L2c+IDwvZz48L3N2Zz4=);
	border-color: #00aadc;
	box-shadow: 0 0 0 2px #78dcfa;
	outline: 0;
	-moz-outline:none;
	-moz-user-focus:ignore;
}

select:disabled,
select:hover:disabled {
	background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiIHN0YW5kYWxvbmU9Im5vIj8+PHN2ZyB3aWR0aD0iMjBweCIgaGVpZ2h0PSIyMHB4IiB2aWV3Qm94PSIwIDAgMjAgMjAiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayIgeG1sbnM6c2tldGNoPSJodHRwOi8vd3d3LmJvaGVtaWFuY29kaW5nLmNvbS9za2V0Y2gvbnMiPiAgICAgICAgPHRpdGxlPmFycm93LWRvd248L3RpdGxlPiAgICA8ZGVzYz5DcmVhdGVkIHdpdGggU2tldGNoLjwvZGVzYz4gICAgPGRlZnM+PC9kZWZzPiAgICA8ZyBpZD0iUGFnZS0xIiBzdHJva2U9Im5vbmUiIHN0cm9rZS13aWR0aD0iMSIgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIiBza2V0Y2g6dHlwZT0iTVNQYWdlIj4gICAgICAgIDxnIGlkPSJhcnJvdy1kb3duIiBza2V0Y2g6dHlwZT0iTVNBcnRib2FyZEdyb3VwIiBmaWxsPSIjZTllZmYzIj4gICAgICAgICAgICA8cGF0aCBkPSJNMTUuNSw2IEwxNyw3LjUgTDEwLjI1LDE0LjI1IEwzLjUsNy41IEw1LDYgTDEwLjI1LDExLjI1IEwxNS41LDYgWiIgaWQ9IkRvd24tQXJyb3ciIHNrZXRjaDp0eXBlPSJNU1NoYXBlR3JvdXAiPjwvcGF0aD4gICAgICAgIDwvZz4gICAgPC9nPjwvc3ZnPg==) no-repeat right 10px center;;
}

select.is-compact {
	min-width: 0;
	padding: 0 20px 2px 6px;
	margin: 0 4px;
	background-position:  right 5px center;
	background-size: 12px 12px;
}

/* Make it display:block when it follows a label */
label select,
label + select {
	display: block;
	min-width: 200px;
}

label select.is-compact,
label + select.is-compact {
	display: inline-block;
	min-width: 0;
}

/* IE: Remove the default arrow */
select::-ms-expand {
	display: none;
}

/* IE: Remove default background and color styles on focus */
select::-ms-value {
	background: none;
	color: #2e4453;
}

/* Firefox: Remove the focus outline, see http://stackoverflow.com/questions/3773430/remove-outline-from-select-box-in-ff/18853002#18853002 */
select:-moz-focusring {
	color: transparent;
	text-shadow: 0 0 0 #2e4453;
}


/* ==========================================================================
** Buttons
** ======================================================================== */

input[type="submit"] {
	background: #fff;
	border-color: #c8d7e1;
	border-style: solid;
	border-width: 1px 1px 2px;
	color: #2e4453;
	cursor: pointer;
	display: inline-block;
	margin: 24px 0 0;
	outline: 0;
	overflow: hidden;
	font-weight: 500;
	text-overflow: ellipsis;
	text-decoration: none;
	vertical-align: top;
	box-sizing: border-box;
	font-size: 14px;
	line-height: 21px;
	border-radius: 4px;
	padding: 7px 14px 9px;
	-webkit-appearance: none;
	-moz-appearance: none;
	appearance: none;
}

input[type="submit"]:hover {
	border-color: #a8bece;
	color: #2e4453;
}

input[type="submit"]:active {
	border-width: 2px 1px 1px;
}

input[type="submit"]:visited {
	color: #2e4453;
}

input[type="submit"][disabled],
input[type="submit"]:disabled {
	color: #e9eff3;
	background: #fff;
	border-color: #e9eff3;
	cursor: default;
}

input[type="submit"][disabled]:active,
input[type="submit"]:disabled:active {
	border-width: 1px 1px 2px;
}

input[type="submit"]:focus {
	border-color: #00aadc;
	box-shadow: 0 0 0 2px #78dcfa;
}

input[type="submit"].hidden {
	display: none;
}

input[type="submit"] .gridicon {
	position: relative;
	top: 4px;
	margin-top: -2px;
	width: 18px;
	height: 18px;
}

input[type="submit"].button-primary {
	background: #00aadc;
	border-color: #008ab3;
	color: #fff;
}

input[type="submit"].button-primary:hover,
input[type="submit"].button-primary:focus {
	border-color: #005082;
	color: #fff;
}

input[type="submit"].button-primary[disabled],
input[type="submit"].button-primary:disabled {
	background: #bceefd;
	border-color: #8cc9e2;
	color: #fff;
}


/* ==========================================================================
** Inline editor styles
** ======================================================================== */


.ui-sortable-handle {
	cursor: move;
}

.grunion-section-header {
	font-size: 21px;
	margin-top: 32px;
	font-weight: 600;
}

.grunion-form-settings:hover {
	box-shadow: 0 0 0 1px rgba(200, 215, 225, 0.5), 0 1px 2px #e9eff3;
}

.grunion-section-header:first-child {
	margin-top: 0;
}

.grunion-type-options {
	display: flex;
	flex-wrap: wrap;
}

.grunion-type {
	flex-grow: 0;
	flex-shrink: 0;
}

.grunion-type select {
	-webkit-appearance: none;
	width: 100%;
}

.grunion-required {
	padding: 27px 0 0 16px;
	flex-grow: 0;
	flex-shrink: 0;
}

.grunion-options {
	padding-top: 16px;
}

.grunion-options ol {
	list-style: none;
	padding: 0;
	margin: 8px 0 0;
}

.grunion-options li {
	display: flex;
	margin-bottom: 16px;
}

.grunion-field-edit .grunion-options {
	display: none;
}

.delete-option,
.delete-field {
	color: #0087be;
	text-decoration: none;
	width: 40px;
	line-height: 40px;
	font-size: 21px;
	text-align: center;
	font-weight: 600;
}

.delete-field {
	position: absolute;
	top: 0;
	right: 0;
}

.grunion-controls {
	display: flex;
	flex-wrap: wrap;
}

.grunion-update-controls {
	text-align: right;
	flex-grow: 1;
}

#add-field {
	flex-grow: 0;
}

.delete-option::before,
.delete-field::before {
	font-family: dashicons;

/*	content: "\f158"; /* This is the bolder X */
	content: "\f335"; /* This is the thinner X */
	display: inline-block;
	speak: none;
}

.grunion-field-edit.grunion-field-checkbox-multiple .grunion-options,
.grunion-field-edit.grunion-field-radio .grunion-options,
.grunion-field-edit.grunion-field-select .grunion-options {
	display: block;
}

.screen-reader-text {
	position: absolute;
	margin: -1px;
	padding: 0;
	height: 1px;
	width: 1px;
	overflow: hidden;
	clip: rect(0 0 0 0);
	border: 0;
	word-wrap: normal !important; /* many screen reader and browser combinations announce broken words as they would appear visually */
}

<?php
/**
 * Master script to create all travel packages
 * This script runs all individual package creation scripts
 */

echo "=== Creating Travel Packages from Itineraries ===\n\n";

// Include WordPress
require_once('wp-config.php');
require_once('wp-load.php');

echo "WordPress loaded successfully.\n\n";

// Check if Advanced Product plugin is active
if (!class_exists('Advanced_Product')) {
    echo "ERROR: Advanced Product plugin is not active. Please activate it first.\n";
    exit;
}

echo "Advanced Product plugin is active.\n\n";

// Create packages
$packages = array(
    'travel-package-1.php' => 'South African Adventure – Child Friendly and Fabulous',
    'travel-package-2.php' => 'South African Luxury Experience',
    'travel-package-3.php' => 'South African Classic Tour',
    'travel-package-4.php' => 'Garden Route & Safari Experience'
);

foreach ($packages as $script => $title) {
    echo "Creating package: $title\n";
    echo "Running script: $script\n";
    
    if (file_exists($script)) {
        ob_start();
        include $script;
        $output = ob_get_clean();
        echo $output;
    } else {
        echo "ERROR: Script file $script not found.\n";
    }
    
    echo "\n" . str_repeat("-", 50) . "\n\n";
}

echo "=== Package Creation Complete ===\n";
echo "All travel packages have been created and added to your WordPress site.\n";
echo "You can now view them in the WordPress admin under Advanced Products.\n\n";

echo "Next steps:\n";
echo "1. Add featured images to each package\n";
echo "2. Review and customize the content as needed\n";
echo "3. Set up booking forms if required\n";
echo "4. Configure pricing and availability\n";
echo "5. Test the packages on the frontend\n";
?>

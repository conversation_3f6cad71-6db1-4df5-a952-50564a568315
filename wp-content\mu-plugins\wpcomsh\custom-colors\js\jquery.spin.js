/**
 * Copyright (c) 2011-2013 <PERSON>
 * Licensed under the MIT license
 */

/*

Basic Usage:
============

$('#el').spin(); // Creates a default Spinner using the text color of #el.
$('#el').spin({ ... }); // Creates a Spinner using the provided options.

$('#el').spin(false); // Stops and removes the spinner.

Using Presets:
==============

$('#el').spin('small'); // Creates a 'small' Spinner using the text color of #el.
$('#el').spin('large', '#fff'); // Creates a 'large' white Spinner.

Adding a custom preset:
=======================

$.fn.spin.presets.flower = {
  lines: 9
  length: 10
  width: 20
  radius: 0
}

$('#el').spin('flower', 'red');

*/

( function ( factory ) {
	if ( typeof exports == 'object' ) {
		// CommonJS
		factory( require( 'jquery' ), require( 'spin' ) );
	} else if ( typeof define == 'function' && define.amd ) {
		// AMD, register as anonymous module
		define( [ 'jquery', 'spin' ], factory );
	} else {
		// Browser globals
		if ( ! window.Spinner ) throw new Error( 'Spin.js not present' );
		factory( window.jQuery, window.Spinner );
	}
} )( function ( $, Spinner ) {
	$.fn.spin = function ( opts, color ) {
		return this.each( function () {
			var $this = $( this ),
				data = $this.data();

			if ( data.spinner ) {
				data.spinner.stop();
				delete data.spinner;
			}
			if ( opts !== false ) {
				opts = $.extend(
					{ color: color || $this.css( 'color' ) },
					$.fn.spin.presets[ opts ] || opts
				);
				// Begin WordPress Additions
				// To use opts.right, you need to have specified a length, width, and radius.
				if (
					typeof opts.right !== 'undefined' &&
					typeof opts.length !== 'undefined' &&
					typeof opts.width !== 'undefined' &&
					typeof opts.radius !== 'undefined'
				) {
					var pad = $this.css( 'padding-left' );
					pad = typeof pad === 'undefined' ? 0 : parseInt( pad, 10 );
					opts.left =
						$this.outerWidth() - 2 * ( opts.length + opts.width + opts.radius ) - pad - opts.right;
					delete opts.right;
				}
				// End WordPress Additions
				data.spinner = new Spinner( opts ).spin( this );
			}
		} );
	};

	$.fn.spin.presets = {
		tiny: { lines: 8, length: 2, width: 2, radius: 3 },
		small: { lines: 8, length: 4, width: 3, radius: 5 },
		large: { lines: 10, length: 8, width: 4, radius: 8 },
	};
} );

// Jetpack Presets Overrides:
( function ( $ ) {
	$.fn.spin.presets.wp = { trail: 60, speed: 1.3 };
	$.fn.spin.presets.small = $.extend(
		{ lines: 8, length: 2, width: 2, radius: 3 },
		$.fn.spin.presets.wp
	);
	$.fn.spin.presets.medium = $.extend(
		{ lines: 8, length: 4, width: 3, radius: 5 },
		$.fn.spin.presets.wp
	);
	$.fn.spin.presets.large = $.extend(
		{ lines: 10, length: 6, width: 4, radius: 7 },
		$.fn.spin.presets.wp
	);
	$.fn.spin.presets[ 'small-left' ] = $.extend( { left: 5 }, $.fn.spin.presets.small );
	$.fn.spin.presets[ 'small-right' ] = $.extend( { right: 5 }, $.fn.spin.presets.small );
	$.fn.spin.presets[ 'medium-left' ] = $.extend( { left: 5 }, $.fn.spin.presets.medium );
	$.fn.spin.presets[ 'medium-right' ] = $.extend( { right: 5 }, $.fn.spin.presets.medium );
	$.fn.spin.presets[ 'large-left' ] = $.extend( { left: 5 }, $.fn.spin.presets.large );
	$.fn.spin.presets[ 'large-right' ] = $.extend( { right: 5 }, $.fn.spin.presets.large );
} )( jQuery );

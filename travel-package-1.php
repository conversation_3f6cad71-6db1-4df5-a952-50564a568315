<?php
/**
 * <PERSON><PERSON><PERSON> to create Travel Package 1: South African Adventure – Child Friendly and Fabulous
 * This script creates a new Advanced Product post for the travel package
 */

// Include WordPress
require_once('wp-config.php');
require_once('wp-load.php');

// Package 1 Data
$package_data = array(
    'title' => 'South African Adventure – Child Friendly and Fabulous',
    'duration' => '12 Days',
    'price_adult' => '$4999',
    'price_child' => '$3500',
    'accommodation_level' => '4*-5*',
    'starting_location' => 'Dubai',
    'ending_location' => 'Dubai',
    'highlights' => array(
        'Child-friendly activities throughout',
        'VIP Safari experiences in Kruger',
        'Cape Town penguin colony visit',
        'Garden Route whale watching',
        'Luxury accommodations',
        'All meals and transfers included'
    ),
    'itinerary' => array(
        array(
            'day' => 1,
            'location' => 'Dubai to Johannesburg',
            'accommodation' => 'Sandton City Hotel (4*-5*)',
            'activities' => 'Emirates flight: Dubai to Johannesburg, Private airport transfer with child-friendly welcome packs',
            'tip' => 'Ensure a smooth arrival with dedicated family services'
        ),
        array(
            'day' => 2,
            'location' => 'Johannesburg',
            'accommodation' => 'Sandton City Hotel',
            'activities' => 'Acrobranch Treetop Adventure, Farm-to-Fork Experience Long Table Lunch',
            'tip' => 'Opt for an interactive farm animal encounter for added excitement'
        ),
        array(
            'day' => 3,
            'location' => 'Kruger Area',
            'accommodation' => 'Tingana Collection Lodge',
            'activities' => 'VIP Ranger Meet-and-Greet, Transfer to Lodge, Sunset Safari',
            'tip' => 'Nature walk focused on animal tracks & sounds'
        ),
        array(
            'day' => 4,
            'location' => 'Kruger Area',
            'accommodation' => 'Tingana Collection Lodge',
            'activities' => 'Safari, Bush Breakfast, Junior Ranger Experience',
            'tip' => 'Storytelling around a campfire'
        ),
        array(
            'day' => 5,
            'location' => 'Cape Town',
            'accommodation' => 'Cape Town Hotel',
            'activities' => 'Flight to Cape Town, Sunset Boat Cruise',
            'tip' => 'Fish-spotting guides for kids'
        ),
        array(
            'day' => 6,
            'location' => 'Cape Town',
            'accommodation' => 'Cape Town Hotel',
            'activities' => 'Children\'s Museum, Culinary Tour, Beach Playtime, Classic Car Adventure',
            'tip' => 'Early dinner option for young travelers'
        ),
        array(
            'day' => 7,
            'location' => 'Cape Town',
            'accommodation' => 'Cape Town Hotel',
            'activities' => 'Visit the penguins, Local Snack Sampling lunch followed by Seafood Foraging',
            'tip' => 'Coastal walk for young explorers'
        ),
        array(
            'day' => 8,
            'location' => 'Cape Winelands',
            'accommodation' => 'Stellenbosch Boutique Accommodation',
            'activities' => 'Boutique Vineyard Tour, Grape-Juice Blending for Kids',
            'tip' => 'Outdoor picnic setting'
        ),
        array(
            'day' => 9,
            'location' => 'Garden Route',
            'accommodation' => 'Stellenbosch Boutique Accommodation',
            'activities' => 'Whale-Watching, Historic Town Exploration, Nature Walks',
            'tip' => 'Travel-friendly play kits'
        ),
        array(
            'day' => 10,
            'location' => 'Knysna',
            'accommodation' => 'Luxury Lifestyle Hotel',
            'activities' => 'Lagoon-Side Dining, Cango Caves Tour',
            'tip' => 'Evening walk for relaxation'
        ),
        array(
            'day' => 11,
            'location' => 'Garden Route',
            'accommodation' => 'Luxury Lifestyle Hotel',
            'activities' => 'Ostrich Farm Visit, Wildlife Sanctuary, Nature Hike',
            'tip' => 'Nature scavenger hunt for kids'
        ),
        array(
            'day' => 12,
            'location' => 'Cape Town/Dubai',
            'accommodation' => 'N/A',
            'activities' => 'Luxury dining experience, Flight to Dubai',
            'tip' => 'Child-friendly farewell treat'
        )
    ),
    'inclusions' => array(
        '11 nights accommodation',
        '11 breakfasts, 10 lunches, 5 dinners',
        'Private transfers',
        'Domestic flights',
        'All tours & activities mentioned'
    )
);

// Create the post content
$content = '<h2>Overview</h2>';
$content .= '<p>Experience the magic of South Africa with your family on this specially designed 12-day adventure that combines wildlife encounters, cultural experiences, and child-friendly activities. From the bustling city of Johannesburg to the wildlife-rich Kruger National Park, the stunning Cape Peninsula, and the scenic Garden Route, this tour offers unforgettable memories for the whole family.</p>';

$content .= '<h2>Tour Highlights</h2>';
$content .= '<ul>';
foreach ($package_data['highlights'] as $highlight) {
    $content .= '<li>' . $highlight . '</li>';
}
$content .= '</ul>';

$content .= '<h2>Detailed Itinerary</h2>';
foreach ($package_data['itinerary'] as $day) {
    $content .= '<h3>Day ' . $day['day'] . ': ' . $day['location'] . '</h3>';
    $content .= '<p><strong>Accommodation:</strong> ' . $day['accommodation'] . '</p>';
    $content .= '<p><strong>Activities:</strong> ' . $day['activities'] . '</p>';
    $content .= '<p><strong>Tingana Tip:</strong> ' . $day['tip'] . '</p>';
}

$content .= '<h2>What\'s Included</h2>';
$content .= '<ul>';
foreach ($package_data['inclusions'] as $inclusion) {
    $content .= '<li>' . $inclusion . '</li>';
}
$content .= '</ul>';

$content .= '<h2>Pricing</h2>';
$content .= '<p><strong>From ' . $package_data['price_adult'] . ' per adult | ' . $package_data['price_child'] . ' per child</strong></p>';
$content .= '<p><em>Based on family sharing accommodation (Trade Partner Rates)</em></p>';

// Create the post
$post_data = array(
    'post_title' => $package_data['title'],
    'post_content' => $content,
    'post_status' => 'publish',
    'post_type' => 'ap_product',
    'post_author' => 1,
    'meta_input' => array(
        'ap_price' => '4999',
        'ap_product_type' => 'tour',
        'duration' => $package_data['duration'],
        'price_adult' => $package_data['price_adult'],
        'price_child' => $package_data['price_child'],
        'accommodation_level' => $package_data['accommodation_level'],
        'starting_location' => $package_data['starting_location'],
        'ending_location' => $package_data['ending_location']
    )
);

$post_id = wp_insert_post($post_data);

if ($post_id) {
    echo "Successfully created travel package: " . $package_data['title'] . " (ID: $post_id)\n";
    
    // Add to product category if it exists
    $term = get_term_by('name', 'Tours', 'ap_product_cat');
    if (!$term) {
        $term = wp_insert_term('Tours', 'ap_product_cat');
        if (!is_wp_error($term)) {
            $term_id = $term['term_id'];
        }
    } else {
        $term_id = $term->term_id;
    }
    
    if (isset($term_id)) {
        wp_set_post_terms($post_id, array($term_id), 'ap_product_cat');
        echo "Added to Tours category\n";
    }
} else {
    echo "Failed to create travel package\n";
}
?>

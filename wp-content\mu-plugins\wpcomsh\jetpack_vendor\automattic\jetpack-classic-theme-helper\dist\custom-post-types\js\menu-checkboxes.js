!function(){const e={inputs:null,popInputs:null,initialize:function(){e.popInputs=document.querySelectorAll('#nova_menuchecklist-pop input[type="checkbox"]'),e.inputs=document.querySelectorAll('#nova_menuchecklist input[type="checkbox"]'),e.inputs.forEach((c=>{c.addEventListener("change",e.checkOne),c.addEventListener("change",e.syncPop)})),e.isChecked()||e.checkFirst(),e.syncPop()},syncPop:function(){e.popInputs.forEach((e=>{const c=document.querySelector(`#in-nova_menu-${e.value}`);e.checked=!!c&&c.checked}))},isChecked:function(){return Array.from(e.inputs).some((e=>e.checked))},checkFirst:function(){const c=e.inputs[0];c&&(c.checked=!0)},checkOne:function(){const c=this;c.checked?e.inputs.forEach((e=>{e!==c&&(e.checked=!1)})):document.querySelector("#nova_menuchecklist").querySelectorAll('input[type="checkbox"]:checked').length>0?c.checked=!1:e.checkFirst()}};document.addEventListener("DOMContentLoaded",e.initialize)}();
window.FB||(window.FB={}),GrunionFB_i18n=jQuery.extend({nameLabel:"Name",emailLabel:"Email",urlLabel:"Website",commentLabel:"Comment",newLabel:"New Field",optionsLabel:"Options",optionLabel:"Option",firstOptionLabel:"First option",problemGeneratingForm:"Oops, there was a problem generating your form.  You'll likely need to try again.",moveInstructions:"Drag up or down\nto re-arrange",moveLabel:"move",editLabel:"edit",savedMessage:"Saved successfully",requiredLabel:"(required)",exitConfirmMessage:"Are you sure you want to exit the form editor without saving?  Any changes you have made will be lost.",maxNewFields:5,invalidEmail:" is an invalid email address."},GrunionFB_i18n),GrunionFB_i18n.moveInstructions=GrunionFB_i18n.moveInstructions.replace("\n","<br />"),FB.span=jQuery("<span>"),FB.esc_html=function(e){return FB.span.text(e).html()},FB.esc_attr=function(e){return(e=FB.esc_html(e)).replace('"',"&quot;").replace("'","&#039;")},FB.ContactForm=function(){const e={action:"grunion_shortcode",_ajax_nonce:ajax_nonce_shortcode,to:"",subject:"",fields:{}},i={name:{label:GrunionFB_i18n.nameLabel,type:"name",required:!0,options:[],order:"1"},email:{label:GrunionFB_i18n.emailLabel,type:"email",required:!0,options:[],order:"2"},url:{label:GrunionFB_i18n.urlLabel,type:"url",required:!1,options:[],order:"3"},comment:{label:GrunionFB_i18n.commentLabel,type:"textarea",required:!0,options:[],order:"4"}};let n=0;const t=GrunionFB_i18n.maxNewFields;let o,r={},l=0;function a(e,i){try{let n="";for(let t=0;t<r[e].options.length;t++)void 0!==r[e].options[t]&&(n="radio"===i?n+'<div id="fb-radio-'+e+"-"+t+'"><input type="radio" id="fb-field'+e+'" name="radio-'+e+'" /><span>'+FB.esc_html(r[e].options[t])+'</span><div class="clear"></div></div>':"checkbox-multiple"===i?n+'<div id="fb-checkbox-multiple-'+e+"-"+t+'"><input type="checkbox" id="fb-field'+e+'" name="checkbox-multiple-'+e+'" /><span>'+FB.esc_html(r[e].options[t])+'</span><div class="clear"></div></div>':n+'<option id="fb-'+e+"-"+t+'" value="'+e+"-"+t+'">'+FB.esc_html(r[e].options[t])+"</option>");return n}catch(e){}}function d(i){try{scroll(0,0),setTimeout((function(){jQuery("#fb-new-label").focus().select()}),100),function(i){try{const n=e.fields[i].type;if(jQuery("#fb-options").hide(),jQuery("#fb-field-id").val(i),jQuery("#fb-new-label").val(e.fields[i].label),jQuery("#fb-new-type").val(e.fields[i].type),e.fields[i].required?jQuery("#fb-new-required").prop("checked",!0):jQuery("#fb-new-required").prop("checked",!1),"select"===n||"radio"===n||"checkbox-multiple"===n){const n=e.fields[i].options;jQuery("#fb-options").show(),jQuery("#fb-new-options").html("");for(let t=0;t<n.length;t++)void 0!==n[t]&&jQuery("#fb-new-options").append('<div id="fb-option-box-'+t+'" class="fb-new-fields"><span optionid="'+t+'" class="fb-remove-option"></span><label></label><input type="text" id="fb-option'+t+'" optionid="'+t+'" value="'+FB.esc_attr(e.fields[i].options[t])+'" class="fb-options" /><div>')}s()}catch(e){}}(i.parent().attr("fieldid"))}catch(e){}}function s(){jQuery("#fb-desc").hide(),jQuery("#fb-add-field").show()}function f(){try{return jQuery("#TB_imageOff",window.parent.document).unbind("click"),jQuery("#TB_closeWindowButton",window.parent.document).unbind("click"),jQuery("#TB_window",window.parent.document).fadeOut("fast"),jQuery("body",window.parent.document).removeClass("modal-open"),jQuery("#TB_window,#TB_overlay,#TB_HideSelect",window.parent.document).trigger("unload").unbind().remove(),jQuery("#TB_load",window.parent.document).remove(),void 0===window.parent.document.body.style.maxHeight&&(jQuery("body","html",window.parent.document).css({height:"auto",width:"auto"}),jQuery("html",window.parent.document).css("overflow","")),window.parent.document.onkeydown="",window.parent.document.onkeyup="",!1}catch(e){}}function u(e,i){try{"show"===e?(jQuery(".fb-edit-field").is(":visible")&&jQuery(".fb-edit-field").remove(),i.find("label").prepend('<span class="right fb-edit-field" style="font-weight: normal;"><a href="" class="fb-reorder"><div style="display: none;">'+GrunionFB_i18n.moveInstructions+"</div>"+GrunionFB_i18n.moveLabel+'</a>&nbsp;&nbsp;<span style="color: #C7D8DE;">|</span>&nbsp;&nbsp;<a href="" class="fb-edit">'+GrunionFB_i18n.editLabel+"</a></span>")):jQuery(".fb-edit-field").remove()}catch(e){}}function c(){try{const i=jQuery("#fb-field-id").val();jQuery("#fb-options").hide(),void 0===r[i]&&(r[i]={}),r[i].options=e.fields[i].options,e.fields[i].options=[]}catch(e){}}function b(){jQuery("#fb-desc").show(),jQuery("#fb-add-field").hide()}function p(e){try{const i=e||GrunionFB_i18n.savedMessage;jQuery("#fb-success").text(i),jQuery("#fb-success").slideDown("fast"),setTimeout((function(){jQuery("#fb-success").slideUp("fast")}),2500)}catch(e){}}function y(e){try{if("preview"===e){if(!function(e){if(0===e.length)return!0;let i,n=e.split(",");for(i=0;i<n.length;i++)if(!1===m(n[i]))return alert(n[i]+GrunionFB_i18n.invalidEmail),!1;return!0}(jQuery("#fb-field-my-email").val()))return;jQuery("#tab-preview a").addClass("current"),jQuery("#tab-settings a").removeClass("current"),jQuery("#fb-preview-form, #fb-desc").show(),jQuery("#fb-email-settings, #fb-email-desc").hide(),p(GrunionFB_i18n.savedMessage)}else jQuery("#tab-preview a").removeClass("current"),jQuery("#tab-settings a").addClass("current"),jQuery("#fb-preview-form, #fb-desc, #fb-add-field").hide(),jQuery("#fb-email-settings, #fb-email-desc").show(),jQuery("#fb-field-my-email").focus().select()}catch(e){}}function m(e){return/^(?=[a-z0-9@.!#$%&'*+/=?^_`{|}~-]{6,254}$)(?=[a-z0-9.!#$%&'*+/=?^_`{|}~-]{1,64}@)[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*@(?:(?=[a-z0-9-]{1,63}\.)[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+(?=[a-z0-9-]{1,63}$)[a-z0-9](?:[a-z0-9-]*[a-z0-9])?$/i.test(e)}function w(i,n,t){try{const o=jQuery("#fb-field-id").val();i||(i=jQuery("#fb-new-type").val()),n||(n=jQuery("#fb-new-field"+o+" .label-text").text());const d=t?'<span class="label-required">'+GrunionFB_i18n.requiredLabel+"</span>":"",s='<label fieldid="'+o+'" for="fb-field'+o+'"><span class="label-text">'+FB.esc_html(n)+"</span>"+d+"</label>",f='<input type="radio" name="radio-'+o+'" id="fb-field'+o+' "disabled="disabled" />',u='<label fieldid="'+o+'" for="fb-field'+o+'" class="fb-radio-label"><span class="label-text">'+FB.esc_html(n)+"</span>"+d+"</label>",b='<div class="fb-remove fb-remove-small" id="'+o+'"></div>',p='<div class="fb-remove" id="'+o+'"></div>',y='<input type="checkbox" id="fb-field'+o+'" "disabled="disabled" />',m='<input type="checkbox" id="fb-field'+o+'" "disabled="disabled" />',w='<div class="fb-remove fb-remove-small" id="'+o+'"></div>',j='<input type="text" id="fb-field'+o+'" "disabled="disabled" />',v='<textarea id="fb-field'+o+'" "disabled="disabled"></textarea>',Q='<div class="clear"></div>',h='<select id="fb-field'+o+'" fieldid="'+o+'"><option id="fb-'+o+"-"+l+'" value="'+o+"-"+l+'">'+GrunionFB_i18n.firstOptionLabel+"</option></select>";switch(i){case"checkbox":c(),jQuery("#fb-new-field"+o+" .fb-fields").html(b+y+u+Q);break;case"checkbox-multiple":jQuery("#fb-new-field"+o+" .fb-fields").html(s+w+'<div fieldid="'+o+'" id="fb-custom-checkbox-multiple'+o+'"></div>'),void 0!==r[o]&&0!==r[o].options.length?(e.fields[o].options=r[o].options,jQuery("#fb-custom-checkbox-multiple"+o).append(a(o,i))):(jQuery("#fb-new-options").html('<label for="fb-option0">'+GrunionFB_i18n.optionsLabel+'</label><input type="text" id="fb-option0" optionid="0" value="'+GrunionFB_i18n.firstOptionLabel+'" class="fb-options" />'),jQuery("#fb-custom-checkbox-multiple"+o).append('<div id="fb-checkbox-multiple-'+o+'-0">'+m+"<span>"+GrunionFB_i18n.firstOptionLabel+"</span>"+Q+"</div>"),e.fields[o].options[l]=GrunionFB_i18n.firstOptionLabel),jQuery("#fb-options").show(),setTimeout((function(){jQuery("#fb-option0").focus().select()}),100);break;case"email":case"name":case"text":case"url":c(),jQuery("#fb-new-field"+o+" .fb-fields").html(p+s+j);break;case"radio":jQuery("#fb-new-field"+o+" .fb-fields").html(s+b+'<div fieldid="'+o+'" id="fb-custom-radio'+o+'"></div>'),void 0!==r[o]&&0!==r[o].options.length?(e.fields[o].options=r[o].options,jQuery("#fb-custom-radio"+o).append(a(o,i))):(jQuery("#fb-new-options").html('<label for="fb-option0">'+GrunionFB_i18n.optionsLabel+'</label><input type="text" id="fb-option0" optionid="0" value="'+GrunionFB_i18n.firstOptionLabel+'" class="fb-options" />'),jQuery("#fb-custom-radio"+o).append('<div id="fb-radio-'+o+'-0">'+f+"<span>"+GrunionFB_i18n.firstOptionLabel+"</span>"+Q+"</div>"),e.fields[o].options[l]=GrunionFB_i18n.firstOptionLabel),jQuery("#fb-options").show(),setTimeout((function(){jQuery("#fb-option0").focus().select()}),100);break;case"select":jQuery("#fb-new-field"+o+" .fb-fields").html(p+s+h),void 0!==r[o]&&0!==r[o].options.length?(e.fields[o].options=r[o].options,jQuery("#fb-field"+o).html(a(o,i))):(jQuery("#fb-new-options").html('<label for="fb-option0">'+GrunionFB_i18n.optionsLabel+'</label><input type="text" id="fb-option0" optionid="0" value="'+GrunionFB_i18n.firstOptionLabel+'" class="fb-options" />'),e.fields[o].options[l]=GrunionFB_i18n.firstOptionLabel),jQuery("#fb-options").show(),setTimeout((function(){jQuery("#fb-option0").focus().select()}),100);break;case"textarea":c(),jQuery("#fb-new-field"+o+" .fb-fields").html(p+s+v)}e.fields[o].type=i}catch(e){}}return{resizePop:function(){try{const e=jQuery("body",window.parent.document).width(),i=jQuery("body",window.parent.document).height(),n=void 0===document.body.style.maxHeight;jQuery("#TB_window, #TB_iframeContent",window.parent.document).css("width","768px"),jQuery("#TB_window",window.parent.document).css({left:(e-768)/2+"px",top:"23px",position:"absolute",marginLeft:"0"}),n||jQuery("#TB_window, #TB_iframeContent",window.parent.document).css("height",i-73+"px")}catch(e){}},init:function(){let a;window.parent.scroll(0,0),a=jQuery("#edButtonPreview",window.parent.document).hasClass("active")||jQuery("#wp-content-wrap",window.parent.document).hasClass("tmce-active")?(window.dialogArguments||opener||parent||top).tinyMCE.activeEditor.getContent():jQuery("#content",window.parent.document).val();const c={action:"grunion_shortcode_to_json",_ajax_nonce:ajax_nonce_json,post_id:postId,content:a},m=jQuery(document);jQuery.post(ajaxurl,c,(function(n){!function(n){try{e.fields={},n?(jQuery.each(n.fields,(function(i,n){1===parseInt(n.required,10)&&(n.required="true"),e.fields[i]=n})),e.to=n.to,e.subject=n.subject):e.fields=i}catch(e){}}(jQuery.parseJSON(n)),function(){try{e.to&&jQuery("#fb-field-my-email").val(e.to),e.subject&&jQuery("#fb-field-subject").val(e.subject),jQuery.each(e.fields,(function(e,i){jQuery("#fb-extra-fields").before('<div class="fb-new-fields ui-state-default" fieldid="'+e+'" id="fb-new-field'+e+'"><div class="fb-fields"></div></div>'),jQuery("#fb-field-id").val(e),r[e]={},r[e].options=[],"radio"!==i.type&&"select"!==i.type&&"checkbox-multiple"!==i.type||jQuery.each(i.options,(function(i,n){r[e].options[i]=n})),w(i.type,i.label,i.required)}))}catch(e){}}()})),jQuery(".fb-add-field").click((function(){return function(){try{n++,n<=t?(jQuery("#fb-extra-fields").append('<div id="fb-new-field'+n+'" fieldid="'+n+'" class="fb-new-fields"><div class="fb-fields"><div id="'+n+'" class="fb-remove"></div><label fieldid="'+n+'" for="fb-field'+n+'"><span class="label-text">'+GrunionFB_i18n.newLabel+'</span> </label><input type="text" id="fb-field'+n+'" disabled="disabled" /></div></div>'),e.fields[n]={label:GrunionFB_i18n.newLabel,type:"text",required:!1,options:[],order:"5"},n===t&&jQuery("#fb-new-field").hide(),l=0,r={},jQuery("#fb-new-options").html('<label for="fb-option0">'+GrunionFB_i18n.optionsLabel+'</label><input type="text" id="fb-option0" optionid="0" value="'+GrunionFB_i18n.firstOptionLabel+'" class="fb-options" />'),jQuery("#fb-options").hide(),jQuery("#fb-new-label").val(GrunionFB_i18n.newLabel),jQuery("#fb-new-type").val("text"),jQuery("#fb-field-id").val(n),setTimeout((function(){jQuery("#fb-new-label").focus().select()}),100)):jQuery("#fb-new-field").hide()}catch(e){}}(),s(),!1})),jQuery("#fb-new-label").keyup((function(){!function(){try{const i=jQuery("#fb-field-id").val(),n=jQuery("#fb-new-label").val();0===n.length?jQuery("#fb-new-field"+i+" label .label-text").text(GrunionFB_i18n.newLabel):jQuery("#fb-new-field"+i+" label .label-text").text(n),e.fields[i].label=n}catch(e){}}()})),jQuery("#fb-new-type").change((function(){w()})),jQuery("#fb-new-required").click((function(){!function(){try{const i=jQuery("#fb-field-id").val();jQuery("#fb-new-required").is(":checked")?(e.fields[i].required=!0,jQuery("#fb-new-field"+i+" label").append('<span class="label-required">'+GrunionFB_i18n.requiredLabel+"</span>")):(e.fields[i].required=!1,jQuery("#fb-new-field"+i+" label .label-required").remove())}catch(e){}}()})),m.on("click",".fb-remove",(function(){b(),function(i){try{n--;const o=i.attr("id");delete e.fields[o],jQuery("#"+o).parent().parent().remove(),n<=t&&jQuery("#fb-new-field").show()}catch(e){}}(jQuery(this)),function(){try{jQuery.post(ajaxurl,e,(function(e){o=e}))}catch(e){alert(GrunionFB_i18n.problemGeneratingForm)}}()})),jQuery("#fb-preview").submit((function(){return function(){try{jQuery("div#sortable div.fb-new-fields").each((function(i){const n=jQuery(this).attr("fieldid");e.fields[n].order=i})),jQuery.post(ajaxurl,e,(function(e){let i=jQuery("#edButtonPreview",window.parent.document).hasClass("active");i||(i=jQuery("#wp-content-wrap",window.parent.document).hasClass("tmce-active"));const n=window.dialogArguments||opener||parent||top;let t;i?t=n.tinyMCE.activeEditor.getContent():(t=jQuery("#editorcontainer textarea",window.parent.document).val(),"string"!=typeof t&&(t=jQuery(".wp-editor-area",window.parent.document).val()));const o=new RegExp("\\[contact-form\\b.*?\\/?\\](?:[\\s\\S]+?\\[\\/contact-form\\])?");if(e=(e=e.replace(/\n/g," ")).replace(/%26#x002c;/g,","),t.match(o))i?n.tinyMCE.activeEditor.execCommand("mceSetContent",!1,t.replace(o,e)):jQuery("#content",window.parent.document).val(t.replace(o,e));else try{n.send_to_editor(e)}catch{i?n.tinyMCE.activeEditor.execCommand("mceInsertContent",!1,e):jQuery("#content",window.parent.document).val(t+e)}f()}))}catch(e){}}(),!1})),jQuery("#TB_overlay, #TB_closeWindowButton",window.parent.document).mousedown((function(){confirm(GrunionFB_i18n.exitConfirmMessage)&&f()})),m.on("click","#fb-another-option",(function(){!function(){try{l=jQuery("#fb-new-options .fb-options").length;const i=jQuery("#fb-field-id").val(),n=jQuery("#fb-new-type").val();"radio"===n?(jQuery("#fb-new-options").append('<div id="fb-option-box-'+l+'" class="fb-new-fields"><span optionid="'+l+'" class="fb-remove-option"></span><label></label><input type="text" id="fb-option'+l+'" optionid="'+l+'" value="'+GrunionFB_i18n.optionLabel+'" class="fb-options" /><div>'),jQuery("#fb-new-field"+i+" .fb-fields").append('<div id="fb-radio-'+i+"-"+l+'"><input type="radio" disabled="disabled" id="fb-field'+i+'" name="radio-'+i+'" /><span>'+GrunionFB_i18n.optionLabel+'</span><div class="clear"></div></div>')):"checkbox-multiple"===n?(jQuery("#fb-new-options").append('<div id="fb-option-box-'+l+'" class="fb-new-fields"><span optionid="'+l+'" class="fb-remove-option"></span><label></label><input type="text" id="fb-option'+l+'" optionid="'+l+'" value="'+GrunionFB_i18n.optionLabel+'" class="fb-options" /><div>'),jQuery("#fb-new-field"+i+" .fb-fields").append('<div id="fb-checkbox-multiple-'+i+"-"+l+'"><input type="checkbox" disabled="disabled" id="fb-field'+i+'" name="checkbox-multiple-'+i+'" /><span>'+GrunionFB_i18n.optionLabel+'</span><div class="clear"></div></div>')):(jQuery("#fb-new-options").append('<div id="fb-option-box-'+l+'" class="fb-new-fields"><span optionid="'+l+'" class="fb-remove-option"></span><label></label><input type="text" id="fb-option'+l+'" optionid="'+l+'" value="" class="fb-options" /><div>'),jQuery("#fb-field"+i).append('<option id="fb-'+i+"-"+l+'" value="'+i+"-"+l+'"></option>')),e.fields[i].options[l]="",jQuery("#fb-option"+l).focus().select()}catch(e){}}()})),m.on("keyup",".fb-options",(function(){!function(i){try{const n=jQuery("#fb-field-id").val(),t=i.attr("optionid"),o=i.val(),r=jQuery("#fb-new-type").val();"radio"===r?jQuery("#fb-radio-"+n+"-"+t+" span").text(o):"checkbox-multiple"===r?jQuery("#fb-checkbox-multiple-"+n+"-"+t+" span").text(o):jQuery("#fb-"+n+"-"+t).text(o),e.fields[n].options[t]=o}catch(e){}}(jQuery(this))})),m.on("click",".fb-remove-option",(function(){!function(i){try{const n=jQuery("#fb-field-id").val(),t=jQuery("#fb-option"+i).val(),o=jQuery("#fb-new-type").val();jQuery("#fb-option-box-"+i).remove(),"radio"===o?jQuery("#fb-radio-"+n+"-"+i).remove():"checkbox-multiple"===o?jQuery("#fb-checkbox-multiple-"+n+"-"+i).remove():jQuery("#fb-"+n+"-"+i).remove();const r=e.fields[n].options.indexOf(t);-1!==r&&e.fields[n].options.splice(r,1)}catch(e){}}(jQuery(this).attr("optionid"))})),jQuery("#tab-preview a").click((function(){return y("preview"),!1})),jQuery("#fb-prev-form").click((function(){return y("preview"),!1})),jQuery("#tab-settings a").click((function(){return y(),!1})),jQuery("#fb-field-my-email").blur((function(){!function(){try{const i=jQuery("#fb-field-my-email").val();e.to=i}catch(e){}}()})),jQuery("#fb-field-subject").blur((function(){!function(){try{const i=jQuery("#fb-field-subject").val();e.subject=i}catch(e){}}()})),m.on("mouseenter",".fb-form-case .fb-new-fields",(function(){u("show",jQuery(this))})),m.on("mouseleave",".fb-form-case .fb-new-fields",(function(){return u("hide"),!1})),m.on("click",".fb-edit-field",(function(){return d(jQuery(this)),!1})),m.on("click",".fb-edit-field .fb-reorder",(function(){return!1})),m.on("click","#fb-save-field",(function(){return b(),p(),!1})),jQuery("#fb-feedback").click((function(){const e=jQuery(this).attr("href");return window.parent.location=e,!1})),jQuery("#sortable").sortable({axis:"y",handle:".fb-reorder",revert:!0,start:function(){jQuery(".fb-edit-field").hide()}}),jQuery("#draggable").draggable({axis:"y",handle:".fb-reorder",connectToSortable:"#sortable",helper:"clone",revert:"invalid"})}}}();
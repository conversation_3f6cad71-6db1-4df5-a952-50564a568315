.jetpack-portfolio-shortcode {
	clear: both;
	margin: 0;
	overflow: hidden;
	padding: 0;
}

.portfolio-entry {
	float: left;
	margin: 0 0 3em;
	padding: 0;
	width: 100%;
}

/* Column setting */
.portfolio-entry-column-1 {
	width: 100%;
}

.portfolio-entry-column-2 {
	margin-right: 4%;
	width: 48%;
}

.portfolio-entry-column-3 {
	margin-right: 3.5%;
	width: 31%;
}

.portfolio-entry-column-4 {
	margin-right: 3%;
	width: 22%;
}

.portfolio-entry-column-5 {
	margin-right: 2.5%;
	width: 18%;
}

.portfolio-entry-column-6 {
	margin-right: 2%;
	width: 15%;
}

.portfolio-entry-first-item-row {
	clear: both;
}

.portfolio-entry-last-item-row {
	margin-right: 0;
}

@media screen and (max-width:768px) {

	.portfolio-entry-mobile-first-item-row{
		margin-right: 4%;
		width: 48%;
		clear:both;
	}

	.portfolio-entry-first-item-row {
		clear:none;
	}

	.portfolio-entry-mobile-last-item-row{
		width: 48%;
		margin-right: 0;
	}
}

/* Entry Header */
.portfolio-entry-header {
	border: 0;
	margin: 0;
	padding: 0;
}

.portfolio-featured-image {
	margin: 0;
	padding: 0;
}

.portfolio-featured-image img {
	border: 0;
	height: auto;
	max-width: 100%;
	vertical-align: middle;
}

.portfolio-entry-title {
	font-weight: 700;
	margin: 0;
	padding: 0;
}

.portfolio-featured-image + .portfolio-entry-title {
	margin-top: 1.0em;
}

.portfolio-entry-title a {
	border: 0;
	text-decoration: none;
}

/* Entry Meta */
.portfolio-entry-meta {
	margin: 0;
	padding: 0;
}

.portfolio-entry-title + .portfolio-entry-meta {
	margin-top: 0.75em;
}

.portfolio-entry-title + .portfolio-entry-meta:empty {
	margin: 0;
}

.portfolio-entry-meta span,
.portfolio-entry-meta a {
	font-size: 0.9em;
	padding: 0;
}

.portfolio-entry-meta a {
	border: 0;
	text-decoration: none;
}

/* Entry Content */
.portfolio-entry-content {
	margin: 0.75em 0 0;
	padding: 0;
}

.portfolio-entry-content > :last-child {
	margin: 0;
}
(()=>{var e={113:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});var o=n(468);const r=(0,o.forwardRef)((function({icon:e,size:t=24,...n},r){return(0,o.cloneElement)(e,{width:t,height:t,...n,ref:r})}))},512:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var o=n(573),r=n(790);const s=(0,r.jsx)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)(o.<PERSON>,{d:"M19.5 4.5h-7V6h4.44l-5.97 5.97 1.06 1.06L18 7.06v4.44h1.5v-7Zm-13 1a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2v-3H17v3a.5.5 0 0 1-.5.5h-10a.5.5 0 0 1-.5-.5v-10a.5.5 0 0 1 .5-.5h3V5.5h-3Z"})})},941:(e,t,n)=>{t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;const n="color: "+this.color;t.splice(1,0,n,"color: inherit");let o=0,r=0;t[0].replace(/%[a-zA-Z%]/g,(e=>{"%%"!==e&&(o++,"%c"===e&&(r=o))})),t.splice(r,0,n)},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch(e){}},t.load=function(){let e;try{e=t.storage.getItem("debug")}catch(e){}!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG);return e},t.useColors=function(){if("undefined"!=typeof window&&window.process&&("renderer"===window.process.type||window.process.__nwjs))return!0;if("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;let e;return"undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)},t.storage=function(){try{return localStorage}catch(e){}}(),t.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.log=console.debug||console.log||(()=>{}),e.exports=n(212)(t);const{formatters:o}=e.exports;o.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},212:(e,t,n)=>{e.exports=function(e){function t(e){let n,r,s,a=null;function i(...e){if(!i.enabled)return;const o=i,r=Number(new Date),s=r-(n||r);o.diff=s,o.prev=n,o.curr=r,n=r,e[0]=t.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let a=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,((n,r)=>{if("%%"===n)return"%";a++;const s=t.formatters[r];if("function"==typeof s){const t=e[a];n=s.call(o,t),e.splice(a,1),a--}return n})),t.formatArgs.call(o,e);(o.log||t.log).apply(o,e)}return i.namespace=e,i.useColors=t.useColors(),i.color=t.selectColor(e),i.extend=o,i.destroy=t.destroy,Object.defineProperty(i,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==a?a:(r!==t.namespaces&&(r=t.namespaces,s=t.enabled(e)),s),set:e=>{a=e}}),"function"==typeof t.init&&t.init(i),i}function o(e,n){const o=t(this.namespace+(void 0===n?":":n)+e);return o.log=this.log,o}function r(e,t){let n=0,o=0,r=-1,s=0;for(;n<e.length;)if(o<t.length&&(t[o]===e[n]||"*"===t[o]))"*"===t[o]?(r=o,s=n,o++):(n++,o++);else{if(-1===r)return!1;o=r+1,s++,n=s}for(;o<t.length&&"*"===t[o];)o++;return o===t.length}return t.debug=t,t.default=t,t.coerce=function(e){if(e instanceof Error)return e.stack||e.message;return e},t.disable=function(){const e=[...t.names,...t.skips.map((e=>"-"+e))].join(",");return t.enable(""),e},t.enable=function(e){t.save(e),t.namespaces=e,t.names=[],t.skips=[];const n=("string"==typeof e?e:"").trim().replace(" ",",").split(",").filter(Boolean);for(const e of n)"-"===e[0]?t.skips.push(e.slice(1)):t.names.push(e)},t.enabled=function(e){for(const n of t.skips)if(r(e,n))return!1;for(const n of t.names)if(r(e,n))return!0;return!1},t.humanize=n(997),t.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach((n=>{t[n]=e[n]})),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let n=0;for(let t=0;t<e.length;t++)n=(n<<5)-n+e.charCodeAt(t),n|=0;return t.colors[Math.abs(n)%t.colors.length]},t.enable(t.load()),t}},997:e=>{var t=1e3,n=60*t,o=60*n,r=24*o,s=7*r,a=365.25*r;function i(e,t,n,o){var r=t>=1.5*n;return Math.round(e/n)+" "+o+(r?"s":"")}e.exports=function(e,c){c=c||{};var l=typeof e;if("string"===l&&e.length>0)return function(e){if((e=String(e)).length>100)return;var i=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(!i)return;var c=parseFloat(i[1]);switch((i[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return c*a;case"weeks":case"week":case"w":return c*s;case"days":case"day":case"d":return c*r;case"hours":case"hour":case"hrs":case"hr":case"h":return c*o;case"minutes":case"minute":case"mins":case"min":case"m":return c*n;case"seconds":case"second":case"secs":case"sec":case"s":return c*t;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return c;default:return}}(e);if("number"===l&&isFinite(e))return c.long?function(e){var s=Math.abs(e);if(s>=r)return i(e,s,r,"day");if(s>=o)return i(e,s,o,"hour");if(s>=n)return i(e,s,n,"minute");if(s>=t)return i(e,s,t,"second");return e+" ms"}(e):function(e){var s=Math.abs(e);if(s>=r)return Math.round(e/r)+"d";if(s>=o)return Math.round(e/o)+"h";if(s>=n)return Math.round(e/n)+"m";if(s>=t)return Math.round(e/t)+"s";return e+"ms"}(e);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},372:(e,t,n)=>{"use strict";n.d(t,{A:()=>c});var o=n(941);const r=n.n(o)()("dops:analytics");let s,a;window._tkq=window._tkq||[],window.ga=window.ga||function(){(window.ga.q=window.ga.q||[]).push(arguments)},window.ga.l=+new Date;const i={initialize:function(e,t,n){i.setUser(e,t),i.setSuperProps(n),i.identifyUser()},setGoogleAnalyticsEnabled:function(e,t=null){this.googleAnalyticsEnabled=e,this.googleAnalyticsKey=t},setMcAnalyticsEnabled:function(e){this.mcAnalyticsEnabled=e},setUser:function(e,t){a={ID:e,username:t}},setSuperProps:function(e){s=e},assignSuperProps:function(e){s=Object.assign(s||{},e)},mc:{bumpStat:function(e,t){const n=function(e,t){let n="";if("object"==typeof e){for(const t in e)n+="&x_"+encodeURIComponent(t)+"="+encodeURIComponent(e[t]);r("Bumping stats %o",e)}else n="&x_"+encodeURIComponent(e)+"="+encodeURIComponent(t),r('Bumping stat "%s" in group "%s"',t,e);return n}(e,t);i.mcAnalyticsEnabled&&((new Image).src=document.location.protocol+"//pixel.wp.com/g.gif?v=wpcom-no-pv"+n+"&t="+Math.random())},bumpStatWithPageView:function(e,t){const n=function(e,t){let n="";if("object"==typeof e){for(const t in e)n+="&"+encodeURIComponent(t)+"="+encodeURIComponent(e[t]);r("Built stats %o",e)}else n="&"+encodeURIComponent(e)+"="+encodeURIComponent(t),r('Built stat "%s" in group "%s"',t,e);return n}(e,t);i.mcAnalyticsEnabled&&((new Image).src=document.location.protocol+"//pixel.wp.com/g.gif?v=wpcom"+n+"&t="+Math.random())}},pageView:{record:function(e,t){i.tracks.recordPageView(e),i.ga.recordPageView(e,t)}},purchase:{record:function(e,t,n,o,r,s,a){i.ga.recordPurchase(e,t,n,o,r,s,a)}},tracks:{recordEvent:function(e,t){t=t||{},0===e.indexOf("akismet_")||0===e.indexOf("jetpack_")?(s&&(r("- Super Props: %o",s),t=Object.assign(t,s)),r('Record event "%s" called with props %s',e,JSON.stringify(t)),window._tkq.push(["recordEvent",e,t])):r('- Event name must be prefixed by "akismet_" or "jetpack_"')},recordJetpackClick:function(e){const t="object"==typeof e?e:{target:e};i.tracks.recordEvent("jetpack_wpa_click",t)},recordPageView:function(e){i.tracks.recordEvent("akismet_page_view",{path:e})},setOptOut:function(e){r("Pushing setOptOut: %o",e),window._tkq.push(["setOptOut",e])}},ga:{initialized:!1,initialize:function(){let e={};i.ga.initialized||(a&&(e={userId:"u-"+a.ID}),window.ga("create",this.googleAnalyticsKey,"auto",e),i.ga.initialized=!0)},recordPageView:function(e,t){i.ga.initialize(),r("Recording Page View ~ [URL: "+e+"] [Title: "+t+"]"),this.googleAnalyticsEnabled&&(window.ga("set","page",e),window.ga("send",{hitType:"pageview",page:e,title:t}))},recordEvent:function(e,t,n,o){i.ga.initialize();let s="Recording Event ~ [Category: "+e+"] [Action: "+t+"]";void 0!==n&&(s+=" [Option Label: "+n+"]"),void 0!==o&&(s+=" [Option Value: "+o+"]"),r(s),this.googleAnalyticsEnabled&&window.ga("send","event",e,t,n,o)},recordPurchase:function(e,t,n,o,r,s,a){window.ga("require","ecommerce"),window.ga("ecommerce:addTransaction",{id:e,revenue:o,currency:a}),window.ga("ecommerce:addItem",{id:e,name:t,sku:n,price:r,quantity:s}),window.ga("ecommerce:send")}},identifyUser:function(){a&&window._tkq.push(["identifyUser",a.ID,a.username])},setProperties:function(e){window._tkq.push(["setProperties",e])},clearedIdentity:function(){window._tkq.push(["clearIdentity"])}},c=i},985:(e,t,n)=>{"use strict";n.d(t,{st:()=>o.A});n(810),n(815),n(409),n(34),n(595),n(265);var o=n(489);n(119),n(406),n(923),n(335),n(290),n(61),n(929),n(765)},765:(e,t,n)=>{"use strict";n(490)},810:(e,t,n)=>{"use strict";n(377).T["Jetpack Green 40"]},335:(e,t,n)=>{"use strict";n(468)},815:(e,t,n)=>{"use strict";n(999)},489:(e,t,n)=>{"use strict";n.d(t,{A:()=>c});var o=n(372),r=n(384),s=n(468);const{tracks:a}=o.A,{recordEvent:i}=a,c=({pageViewEventName:e=null,pageViewNamespace:t="jetpack",pageViewSuffix:n="page_view",pageViewEventProperties:c={}}={})=>{const[l,u]=(0,s.useState)(!1),{isUserConnected:d,isRegistered:p,userConnectionData:f={}}=(0,r.useConnection)(),{wpcomUser:{login:m,ID:g}={},blogId:w}=f.currentUser||{},C=(0,s.useCallback)((async(e,t={})=>{d&&g&&m&&i(e,t)}),[d,g,m]);return(0,s.useEffect)((()=>{d&&g&&m&&w&&o.A.initialize(g,m,{blog_id:w})}),[w,g,m,d]),(0,s.useEffect)((()=>{const o=e?`${t}_${e}_${n}`:null;p&&o&&(l||(C(o,c),u(!0)))}),[l,t,e,n,p,c,C]),{recordEvent:C,tracks:a}}},119:(e,t,n)=>{"use strict";n(143),n(468),n(87)},923:(e,t,n)=>{"use strict";n(143),n(468),n(290)},406:(e,t,n)=>{"use strict";n(468)},929:(e,t,n)=>{"use strict";n(143),n(619),n(265),n(119)},520:(e,t,n)=>{"use strict";var o=n(941),r=n.n(o);window,r()("shared-extension-utils:connection")},61:(e,t,n)=>{"use strict";n(520)},105:(e,t,n)=>{"use strict";n.d(t,{Ay:()=>d,E9:()=>u});var o=n(143),r=n(634),s=n(478),a=n(290);const i="SET_JETPACK_MODULES";function c(e){return u({isLoading:e})}function l(e,t){return{type:"SET_MODULE_UPDATING",name:e,isUpdating:t}}function u(e){return{type:i,options:e}}const d={updateJetpackModuleStatus:function*(e){try{yield l(e.name,!0),yield(0,s.sB)(e);const t=yield(0,s.wz)();return yield u({data:t}),!0}catch{const e=(0,o.select)(a.F).getJetpackModules();return yield u(e),!1}finally{yield l(e.name,!1)}},setJetpackModules:u,fetchModules:function*(){if((0,r.Sy)())return!0;try{yield c(!0);const e=yield(0,s.wz)();return yield u({data:e}),!0}catch{const e=(0,o.select)(a.F).getJetpackModules();return yield u(e),!1}finally{yield c(!1)}}}},478:(e,t,n)=>{"use strict";n.d(t,{Ay:()=>l,sB:()=>c,wz:()=>i});var o=n(455),r=n.n(o);const s="FETCH_JETPACK_MODULES",a="UPDATE_JETPACK_MODULE_STATUS",i=()=>({type:s}),c=e=>({type:a,settings:e}),l={[s]:function(){return r()({path:"/jetpack/v4/module/all",method:"GET"})},[a]:function({settings:e}){return r()({path:`/jetpack/v4/module/${e.name}/active`,method:"POST",data:{active:e.active}})}}},290:(e,t,n)=>{"use strict";n.d(t,{F:()=>l});var o=n(143),r=n(105),s=n(478),a=n(862),i=n(701),c=n(640);const l="jetpack-modules",u=(0,o.createReduxStore)(l,{reducer:a.A,actions:r.Ay,controls:s.Ay,resolvers:i.A,selectors:c.A});(0,o.register)(u);const d=window?.Initial_State?.getModules||window?.Jetpack_Editor_Initial_State?.modules||null;null!==d&&(0,o.dispatch)(l).setJetpackModules({data:{...d}})},862:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});const o={isLoading:!1,isUpdating:{},data:{}},r=(e=o,t)=>{switch(t.type){case"SET_JETPACK_MODULES":return{...e,...t.options};case"SET_MODULE_UPDATING":return{...e,isUpdating:{...e.isUpdating,[t.name]:t.isUpdating}}}return e}},701:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var o=n(105),r=n(478);const s={getJetpackModules:function*(){try{const e=yield(0,r.wz)();if(e)return(0,o.E9)({data:e})}catch(e){console.error(e)}}}},640:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});var o=n(634);const r={getJetpackModules:e=>e.data,isModuleActive:(e,t)=>(0,o.Sy)()||(e?.data?.[t]?.activated??!1),areModulesLoading:e=>e.isLoading??!1,isModuleUpdating:(e,t)=>e?.isUpdating?.[t]??!1}},265:(e,t,n)=>{"use strict";var o=n(723);n(832),n(87),n(815);const __=o.__;__("Upgrade your plan to use video covers","jetpack-blaze"),__("Upgrade your plan to upload audio","jetpack-blaze")},34:(e,t,n)=>{"use strict";n(279)},409:(e,t,n)=>{"use strict";n(999)},634:(e,t,n)=>{"use strict";function o(){return"object"==typeof window&&"string"==typeof window._currentSiteType?window._currentSiteType:null}function r(){return"simple"===o()}n.d(t,{Sy:()=>r})},595:(e,t,n)=>{"use strict";n(72),n(491)},368:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});var o=n(427);function r(){return React.createElement(o.SVG,{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},React.createElement(o.G,{clipPath:"url(#clip0_4728_49296)"},React.createElement("circle",{cx:"12",cy:"12",r:"12",fill:"#D9D9D9"}),React.createElement("circle",{cx:"12",cy:"12",r:"12",fill:"black"}),React.createElement(o.Path,{d:"M14.8665 6.68544C14.7139 6.50468 14.4652 6.43826 14.2436 6.52015C14.0224 6.60204 13.875 6.81412 13.875 7.05149V8.71684C13.875 9.02893 13.6226 9.2829 13.3125 9.2829C13.0024 9.2829 12.75 9.02893 12.75 8.71684V4.72989C12.75 4.56762 12.681 4.41365 12.5602 4.3061C12.4354 4.19515 12.3622 4.13288 12.3622 4.13288C12.1537 3.95627 11.8485 3.95552 11.6389 4.13175C11.4086 4.32572 6 8.9327 6 13.434C6 16.7632 8.69175 19.472 12 19.472C15.3082 19.472 18 16.7632 18 13.434C18 10.9267 16.296 8.38022 14.8665 6.68544ZM12.0728 18.5274C11.508 18.544 10.9699 18.3259 10.5259 17.9742C8.3535 16.2537 10.3856 13.4982 11.4469 12.3106C11.7435 11.9789 12.2584 11.9807 12.555 12.3125C13.2742 13.1178 14.4375 14.6416 14.4375 16.0756C14.4375 17.4059 13.3853 18.4885 12.0728 18.5274Z",fill:"white"})))}},384:e=>{"use strict";e.exports=window.JetpackConnection},999:e=>{"use strict";e.exports=window.JetpackScriptDataModule},790:e=>{"use strict";e.exports=window.ReactJSXRuntime},87:e=>{"use strict";e.exports=window.lodash},455:e=>{"use strict";e.exports=window.wp.apiFetch},427:e=>{"use strict";e.exports=window.wp.components},491:e=>{"use strict";e.exports=window.wp.compose},143:e=>{"use strict";e.exports=window.wp.data},490:e=>{"use strict";e.exports=window.wp.domReady},309:e=>{"use strict";e.exports=window.wp.editPost},656:e=>{"use strict";e.exports=window.wp.editor},468:e=>{"use strict";e.exports=window.wp.element},619:e=>{"use strict";e.exports=window.wp.hooks},723:e=>{"use strict";e.exports=window.wp.i18n},279:e=>{"use strict";e.exports=window.wp.plugins},573:e=>{"use strict";e.exports=window.wp.primitives},832:e=>{"use strict";e.exports=window.wp.url},72:e=>{function t(){return e.exports=t=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)({}).hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},e.exports.__esModule=!0,e.exports.default=e.exports,t.apply(null,arguments)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},377:e=>{"use strict";e.exports=JSON.parse('{"T":{"White":"#fff","Black":"#000","Gray 0":"#f6f7f7","Gray 5":"#dcdcde","Gray 10":"#c3c4c7","Gray 20":"#a7aaad","Gray 30":"#8c8f94","Gray 40":"#787c82","Gray 50":"#646970","Gray 60":"#50575e","Gray 70":"#3c434a","Gray 80":"#2c3338","Gray 90":"#1d2327","Gray 100":"#101517","Gray":"#646970","Blue 0":"#fbfcfe","Blue 5":"#f7f8fe","Blue 10":"#d6ddf9","Blue 20":"#adbaf3","Blue 30":"#7b90ff","Blue 40":"#546ff3","Blue 50":"#3858e9","Blue 60":"#2a46ce","Blue 70":"#1d35b4","Blue 80":"#1f3286","Blue 90":"#14215a","Blue 100":"#0a112d","Blue":"#3858e9","WordPress Blue 0":"#fbfcfe","WordPress Blue 5":"#f7f8fe","WordPress Blue 10":"#d6ddf9","WordPress Blue 20":"#adbaf3","WordPress Blue 30":"#7b90ff","WordPress Blue 40":"#546ff3","WordPress Blue 50":"#3858e9","WordPress Blue 60":"#2a46ce","WordPress Blue 70":"#1d35b4","WordPress Blue 80":"#1f3286","WordPress Blue 90":"#14215a","WordPress Blue 100":"#0a112d","WordPress Blue":"#3858e9","Purple 0":"#f2e9ed","Purple 5":"#ebcee0","Purple 10":"#e3afd5","Purple 20":"#d48fc8","Purple 30":"#c475bd","Purple 40":"#b35eb1","Purple 50":"#984a9c","Purple 60":"#7c3982","Purple 70":"#662c6e","Purple 80":"#4d2054","Purple 90":"#35163b","Purple 100":"#1e0c21","Purple":"#984a9c","Pink 0":"#f5e9ed","Pink 5":"#f2ceda","Pink 10":"#f7a8c3","Pink 20":"#f283aa","Pink 30":"#eb6594","Pink 40":"#e34c84","Pink 50":"#c9356e","Pink 60":"#ab235a","Pink 70":"#8c1749","Pink 80":"#700f3b","Pink 90":"#4f092a","Pink 100":"#260415","Pink":"#c9356e","Red 0":"#f7ebec","Red 5":"#facfd2","Red 10":"#ffabaf","Red 20":"#ff8085","Red 30":"#f86368","Red 40":"#e65054","Red 50":"#d63638","Red 60":"#b32d2e","Red 70":"#8a2424","Red 80":"#691c1c","Red 90":"#451313","Red 100":"#240a0a","Red":"#d63638","Orange 0":"#f5ece6","Orange 5":"#f7dcc6","Orange 10":"#ffbf86","Orange 20":"#faa754","Orange 30":"#e68b28","Orange 40":"#d67709","Orange 50":"#b26200","Orange 60":"#8a4d00","Orange 70":"#704000","Orange 80":"#543100","Orange 90":"#361f00","Orange 100":"#1f1200","Orange":"#b26200","Yellow 0":"#f5f1e1","Yellow 5":"#f5e6b3","Yellow 10":"#f2d76b","Yellow 20":"#f0c930","Yellow 30":"#deb100","Yellow 40":"#c08c00","Yellow 50":"#9d6e00","Yellow 60":"#7d5600","Yellow 70":"#674600","Yellow 80":"#4f3500","Yellow 90":"#320","Yellow 100":"#1c1300","Yellow":"#9d6e00","Green 0":"#e6f2e8","Green 5":"#b8e6bf","Green 10":"#68de86","Green 20":"#1ed15a","Green 30":"#00ba37","Green 40":"#00a32a","Green 50":"#008a20","Green 60":"#007017","Green 70":"#005c12","Green 80":"#00450c","Green 90":"#003008","Green 100":"#001c05","Green":"#008a20","Celadon 0":"#e4f2ed","Celadon 5":"#a7e8d3","Celadon 10":"#66deb9","Celadon 20":"#31cc9f","Celadon 30":"#09b585","Celadon 40":"#009e73","Celadon 50":"#008763","Celadon 60":"#007053","Celadon 70":"#005c44","Celadon 80":"#004533","Celadon 90":"#003024","Celadon 100":"#001c15","Celadon":"#008763","Automattic Blue 0":"#ebf4fa","Automattic Blue 5":"#c4e2f5","Automattic Blue 10":"#88ccf2","Automattic Blue 20":"#5ab7e8","Automattic Blue 30":"#24a3e0","Automattic Blue 40":"#1490c7","Automattic Blue 50":"#0277a8","Automattic Blue 60":"#036085","Automattic Blue 70":"#02506e","Automattic Blue 80":"#02384d","Automattic Blue 90":"#022836","Automattic Blue 100":"#021b24","Automattic Blue":"#24a3e0","Simplenote Blue 0":"#e9ecf5","Simplenote Blue 5":"#ced9f2","Simplenote Blue 10":"#abc1f5","Simplenote Blue 20":"#84a4f0","Simplenote Blue 30":"#618df2","Simplenote Blue 40":"#4678eb","Simplenote Blue 50":"#3361cc","Simplenote Blue 60":"#1d4fc4","Simplenote Blue 70":"#113ead","Simplenote Blue 80":"#0d2f85","Simplenote Blue 90":"#09205c","Simplenote Blue 100":"#05102e","Simplenote Blue":"#3361cc","WooCommerce Purple 0":"#f2edff","WooCommerce Purple 5":"#e1d7ff","WooCommerce Purple 10":"#d1c1ff","WooCommerce Purple 20":"#b999ff","WooCommerce Purple 30":"#a77eff","WooCommerce Purple 40":"#873eff","WooCommerce Purple 50":"#720eec","WooCommerce Purple 60":"#6108ce","WooCommerce Purple 70":"#5007aa","WooCommerce Purple 80":"#3c087e","WooCommerce Purple 90":"#2c045d","WooCommerce Purple 100":"#1f0342","WooCommerce Purple":"#720eec","Jetpack Green 0":"#f0f2eb","Jetpack Green 5":"#d0e6b8","Jetpack Green 10":"#9dd977","Jetpack Green 20":"#64ca43","Jetpack Green 30":"#2fb41f","Jetpack Green 40":"#069e08","Jetpack Green 50":"#008710","Jetpack Green 60":"#007117","Jetpack Green 70":"#005b18","Jetpack Green 80":"#004515","Jetpack Green 90":"#003010","Jetpack Green 100":"#001c09","Jetpack Green":"#069e08"}}')}},t={};function n(o){var r=t[o];if(void 0!==r)return r.exports;var s=t[o]={exports:{}};return e[o](s,s.exports,n),s.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var o in t)n.o(t,o)&&!n.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{"use strict";var e=n(985),t=n(427),o=n(491),r=n(143),s=n(309),a=n(656),i=n(468),c=n(723),l=n(113),u=n(512),d=n(279),p=n(368);const __=c.__,f=a.PluginPostPublishPanel||s.PluginPostPublishPanel,m=()=>{const{blazeUrlTemplate:n}=window?.blazeInitialState||{},{tracks:s}=(0,e.st)(),c=(0,i.useCallback)((()=>s.recordEvent("jetpack_editor_blaze_publish_click")),[s]),{isPostPublished:d,isPublishingPost:m,postId:g,postType:w,postVisibility:C}=(0,r.useSelect)((e=>({isPostPublished:e(a.store).isCurrentPostPublished(),isPublishingPost:e(a.store).isPublishingPost(),postId:e(a.store).getCurrentPostId(),postType:e(a.store).getCurrentPostType(),postVisibility:e(a.store).getEditedPostVisibility()}))),b=(0,o.usePrevious)(m),h={name:"blaze-panel",title:__("Promote with Blaze","jetpack-blaze"),className:"blaze-panel",icon:React.createElement(p.A,null),initialOpen:!0},y=n.link.replace("__POST_ID__",g),P=(0,i.useCallback)((()=>!!["page","post","product"].includes(w)&&!(!d||"password"===C||"private"===C)),[w,d,C]);if((0,i.useEffect)((()=>{b&&!m&&P()&&d&&s.recordEvent("jetpack_editor_blaze_post_publish_panel_view")}),[s,m,d,b,P]),!P())return null;const v={page:__("Blaze this page","jetpack-blaze"),post:__("Blaze this post","jetpack-blaze"),product:__("Blaze this product","jetpack-blaze")}[w]??__("Blaze this post","jetpack-blaze");return React.createElement(f,h,React.createElement(t.PanelRow,null,React.createElement("p",null,__("Reach a larger audience boosting the content to the WordPress.com community of blogs and sites.","jetpack-blaze"))),React.createElement("div",{role:"link",className:"post-publish-panel__postpublish-buttons",tabIndex:0,onClick:c,onKeyDown:c},React.createElement(t.Button,{variant:"secondary",href:y,target:"_top"},v,n.external&&React.createElement(l.A,{icon:u.A,className:"blaze-panel-outbound-link__external_icon"}))))};(0,d.getPlugin)("jetpack-blaze")||(0,d.registerPlugin)("jetpack-blaze",{render:m})})()})();
!function(){let e,t;const n=new Set,a={init:function(o){let r=o.lastElementChild;for(;r&&"TBODY"!==r.tagName;)r=r.previousElementSibling;const c=r.querySelector("tr:first-child").cloneNode(!0);return o.dataset.form=o.closest("form"),o.dataset.tbody=r,o.dataset.row=c,o.dataset.currentRow=c,e=document.getElementById("nova-menu-tax"),t=document.getElementById("_wpnonce"),o.addEventListener("keypress",(function(e){13===e.which&&(e.preventDefault(),"function"==typeof FormData&&a.submitRow.call(o),a.addRow.call(o))})),o.addEventListener("focusin",(function(e){"INPUT"!==e.target.tagName&&"TEXTAREA"!==e.target.tagName||(o.dataset.currentRow=e.target.closest("tr"))})),n.add(o),o},destroy:function(e){return e.removeEventListener("keypress",a.keypressHandler),e.removeEventListener("focusin",a.focusinHandler),n.delete(e),e},submitRow:function(n){const a=n.dataset.currentRow,o=a.querySelectorAll("input, textarea, select"),r=document.querySelector(n.dataset.form),c=Array.from(r.querySelectorAll("input, textarea, select"));o.forEach((e=>e.disabled=!0)),c.filter((e=>!o.includes(e))).forEach((e=>e.disabled=!0));const s=new FormData(r);return s.append("ajax","1"),s.append("nova_menu_tax",e.value),s.append("_wpnonce",t.value),fetch("",{method:"POST",body:s}).then((e=>e.text())).then((e=>{a.innerHTML=e})),c.forEach((e=>e.disabled=!1)),n},addRow:function(e){const t=e.dataset.row.cloneNode(!0);e.dataset.tbody.appendChild(t);const n=t.querySelector("input, textarea, select");return n&&n.focus(),e},clickAddRow:function(e){let t=e.lastElementChild;for(;t&&"TBODY"!==t.tagName;)t=t.previousElementSibling;const n=t.querySelector("tr:first-child").cloneNode(!0);n.querySelectorAll("input, textarea").forEach((e=>{e.value=""})),t.appendChild(n)}};document.addEventListener("focusin",(e=>{const t=e.target.closest(".many-items-table");t&&!n.has(t)&&a.init(t)})),document.addEventListener("click",(e=>{if(e.target.matches("a.nova-new-row")){const t=e.target.closest(".many-items-table");t&&(e.preventDefault(),a.clickAddRow(t))}})),new MutationObserver((e=>{e.forEach((e=>{e.removedNodes.forEach((e=>{e.matches&&e.matches(".many-items-table")&&a.destroy(e)}))}))})).observe(document.body,{childList:!0,subtree:!0})}();
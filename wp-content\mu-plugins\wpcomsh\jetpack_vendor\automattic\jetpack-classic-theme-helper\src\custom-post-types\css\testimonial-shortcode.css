.jetpack-testimonial-shortcode {
	clear: both;
	margin: 0;
	overflow: hidden;
	padding: 0;
}

.testimonial-entry {
	float: left;
	margin: 0 0 3em;
	padding: 0;
	width: 100%;
}

/* Column setting */
.testimonial-entry-column-1 {
	width: 100%;
}

.testimonial-entry-column-2 {
	margin-right: 4%;
	width: 48%;
}

.testimonial-entry-column-3 {
	margin-right: 3.5%;
	width: 31%;
}

.testimonial-entry-column-4 {
	margin-right: 3%;
	width: 22%;
}

.testimonial-entry-column-5 {
	margin-right: 2.5%;
	width: 18%;
}

.testimonial-entry-column-6 {
	margin-right: 2%;
	width: 15%;
}

.testimonial-entry-first-item-row {
	clear: both;
}

.testimonial-entry-last-item-row {
	margin-right: 0;
}

@media screen and (max-width:768px) {

	.testimonial-entry-mobile-first-item-row{
		margin-right: 4%;
		width: 48%;
		clear:both;
	}

	.testimonial-entry-first-item-row {
		clear:none;
	}

	.testimonial-entry-mobile-last-item-row{
		width: 48%;
		margin-right: 0;
	}
}

.testimonial-featured-image {
	padding: 0;
	margin: 0;
}

.testimonial-featured-image img {
	border: 0;
	height: auto;
	max-width: 100%;
	vertical-align: middle;
}

.testimonial-entry-title {
	font-weight: 700;
	margin: 0;
	padding: 0;
	display: block;
}

.testimonial-featured-image + .testimonial-entry-title {
	margin-top: 1.0em;
}

.testimonial-entry-title a {
	border: 0;
	text-decoration: none;
}

/* Entry Content */
.testimonial-entry-content {
	margin: 0.75em 0;
	padding: 0;
}

.testimonial-entry-content > :last-child {
	margin: 0;
}
<?php
/**
 * <PERSON><PERSON><PERSON> to create Travel Package 4: Garden Route & Safari Experience
 * This script creates a new Advanced Product post for the Garden Route safari package
 */

// Include WordPress
require_once('wp-config.php');
require_once('wp-load.php');

// Package 4 Data
$package_data = array(
    'title' => 'Garden Route & Safari Experience',
    'duration' => '13 Days',
    'price_adult' => 'Contact for Pricing',
    'price_child' => 'Contact for Pricing',
    'accommodation_level' => '5*',
    'starting_location' => 'Johannesburg',
    'ending_location' => 'Johannesburg',
    'highlights' => array(
        'Luxury 5-star accommodations throughout',
        'Knysna Heads and Featherbed Nature Reserve',
        'Tsitsikamma National Park adventures',
        'Hermanus whale watching (seasonal)',
        'Cape Town city exploration',
        'Kruger National Park Big Five safari',
        'Sandton shopping and dining'
    ),
    'itinerary' => array(
        array(
            'day' => 1,
            'location' => 'Johannesburg',
            'accommodation' => '5* Hotel',
            'activities' => 'Arrival at OR Tambo Airport, Transfer to hotel for overnight stay.',
            'tip' => 'Evening arrival in Johannesburg. Expect a warm welcome and easy transfer to your luxurious hotel for a restful night.'
        ),
        array(
            'day' => 2,
            'location' => 'Johannesburg / George',
            'accommodation' => 'Knysna Hotel',
            'activities' => 'Transfer to OR Tambo Airport for flight to George. Pick up rental car and transfer to hotel.',
            'tip' => 'Domestic flight to George'
        ),
        array(
            'day' => 3,
            'location' => 'Garden Route',
            'accommodation' => 'Knysna Hotel',
            'activities' => 'Explore Knysna Heads, Featherbed Nature Reserve, Knysna Waterfront, Knysna Lagoon, Harold\'s Bay.',
            'tip' => 'Spectacular viewpoints at Knysna Heads, guided hikes or 4x4 drives at Featherbed Nature Reserve, vibrant atmosphere at Knysna Waterfront, kayaking or boating at Knysna Lagoon, and relaxing beach views at Harold\'s Bay.'
        ),
        array(
            'day' => 4,
            'location' => 'Garden Route',
            'accommodation' => 'Knysna Hotel',
            'activities' => 'Visit Tsitsikamma National Park, explore Storms River Mouth and suspension bridge.',
            'tip' => 'Dramatic coastal scenery at Tsitsikamma National Park, with opportunities for hiking and crossing the famous suspension bridge.'
        ),
        array(
            'day' => 5,
            'location' => 'Hermanus',
            'accommodation' => 'Guest House',
            'activities' => 'Drive to Hermanus, check-in at Whale Guest House, coastal walk, whale watching (June–November).',
            'tip' => 'Scenic drive along the coast to Hermanus, coastal walks with breathtaking views, and potential whale sightings during the peak season.'
        ),
        array(
            'day' => 6,
            'location' => 'Cape Town',
            'accommodation' => '5* Hotel',
            'activities' => 'Drive to Cape Town, explore the city, check into hotel in Cape Town.',
            'tip' => 'Enjoy the drive with picturesque landscapes, and immerse yourself in the vibrant culture and history of Cape Town upon arrival.'
        ),
        array(
            'day' => 7,
            'location' => 'Cape Town',
            'accommodation' => '5* Hotel',
            'activities' => 'Sightseeing: Table Mountain, V&A Waterfront, Kirstenbosch Botanical Gardens.',
            'tip' => 'Take in the panoramic views from Table Mountain, enjoy shopping and dining at V&A Waterfront, and explore the lush Kirstenbosch Botanical Gardens with diverse plant species.'
        ),
        array(
            'day' => 8,
            'location' => 'Cape Peninsula',
            'accommodation' => '5* Hotel',
            'activities' => 'Escorted tour: Cape Point, Boulders Beach, Chapman\'s Peak Drive.',
            'tip' => 'Experience the dramatic coastal views at Cape Point, visit the adorable penguin colony at Boulders Beach, and drive along the scenic Chapman\'s Peak.'
        ),
        array(
            'day' => 9,
            'location' => 'Cape Town',
            'accommodation' => '5* Hotel',
            'activities' => 'Sightseeing: Robben Island, Bo-Kaap.',
            'tip' => 'Take a historical tour of Robben Island where Nelson Mandela was imprisoned, and explore the colorful Bo-Kaap neighborhood with its rich cultural heritage.'
        ),
        array(
            'day' => 10,
            'location' => 'Kruger National Park',
            'accommodation' => 'Leopard Sands',
            'activities' => 'Drop off rental car at Cape Town Airport for flight to Skukuza Airport. Transfer to Leopard Sands.',
            'tip' => 'Transition from city to safari, with an afternoon arrival at Leopard Sands, ready to start your wildlife adventure.'
        ),
        array(
            'day' => 11,
            'location' => 'Kruger National Park',
            'accommodation' => 'Leopard Sands',
            'activities' => 'Half-day game drive, evening game drive.',
            'tip' => 'Encounter diverse wildlife during morning and evening safaris, with opportunities to spot the Big Five and other animals in their natural habitat.'
        ),
        array(
            'day' => 12,
            'location' => 'Johannesburg',
            'accommodation' => 'Sandton Hotel',
            'activities' => 'Transfer to Skukuza Airport for flight to Johannesburg, tour of Sandton/Rosebank precinct.',
            'tip' => 'Return to Johannesburg and explore the trendy Melrose Arch area with its upscale shops, restaurants, and lively atmosphere.'
        ),
        array(
            'day' => 13,
            'location' => 'Johannesburg',
            'accommodation' => 'N/A',
            'activities' => 'Transfer to OR Tambo Airport for flight home.',
            'tip' => 'Conclude your trip with a comfortable transfer to the airport for your flight back home.'
        )
    ),
    'inclusions' => array(
        '12 nights luxury accommodation',
        'Breakfast daily',
        'Domestic flights as specified',
        'Rental car for Garden Route portion',
        'All transfers and transportation',
        'Professional guide services',
        'All tours and activities mentioned',
        'Game drives in Kruger National Park'
    )
);

// Create the post content
$content = '<h2>Overview</h2>';
$content .= '<p>Experience the best of South Africa on this comprehensive 13-day journey that combines the scenic beauty of the Garden Route with thrilling safari adventures in Kruger National Park. Stay in luxury 5-star accommodations while exploring dramatic coastlines, charming towns, vibrant cities, and world-renowned wildlife destinations.</p>';

$content .= '<h2>Tour Highlights</h2>';
$content .= '<ul>';
foreach ($package_data['highlights'] as $highlight) {
    $content .= '<li>' . $highlight . '</li>';
}
$content .= '</ul>';

$content .= '<h2>Detailed Itinerary</h2>';
foreach ($package_data['itinerary'] as $day) {
    $content .= '<h3>Day ' . $day['day'] . ': ' . $day['location'] . '</h3>';
    $content .= '<p><strong>Accommodation:</strong> ' . $day['accommodation'] . '</p>';
    $content .= '<p><strong>Activities:</strong> ' . $day['activities'] . '</p>';
    $content .= '<p><strong>What to Expect:</strong> ' . $day['tip'] . '</p>';
}

$content .= '<h2>What\'s Included</h2>';
$content .= '<ul>';
foreach ($package_data['inclusions'] as $inclusion) {
    $content .= '<li>' . $inclusion . '</li>';
}
$content .= '</ul>';

$content .= '<h2>Best Time to Visit</h2>';
$content .= '<p>This tour can be enjoyed year-round, but for whale watching in Hermanus, the best time is June to November when Southern Right Whales visit the coast.</p>';

$content .= '<h2>Pricing</h2>';
$content .= '<p><strong>Contact us for personalized pricing</strong></p>';
$content .= '<p><em>This premium experience includes luxury accommodations and can be customized to your preferences.</em></p>';

// Create the post
$post_data = array(
    'post_title' => $package_data['title'],
    'post_content' => $content,
    'post_status' => 'publish',
    'post_type' => 'ap_product',
    'post_author' => 1,
    'meta_input' => array(
        'ap_price' => '0',
        'ap_price_contact' => 'yes',
        'ap_product_type' => 'luxury_tour',
        'duration' => $package_data['duration'],
        'accommodation_level' => $package_data['accommodation_level'],
        'starting_location' => $package_data['starting_location'],
        'ending_location' => $package_data['ending_location']
    )
);

$post_id = wp_insert_post($post_data);

if ($post_id) {
    echo "Successfully created travel package: " . $package_data['title'] . " (ID: $post_id)\n";
    
    // Add to product categories
    $tour_term = get_term_by('name', 'Tours', 'ap_product_cat');
    $luxury_term = get_term_by('name', 'Luxury Tours', 'ap_product_cat');
    $safari_term = get_term_by('name', 'Safari Tours', 'ap_product_cat');
    
    if (!$tour_term) {
        $tour_term = wp_insert_term('Tours', 'ap_product_cat');
        if (!is_wp_error($tour_term)) {
            $tour_term_id = $tour_term['term_id'];
        }
    } else {
        $tour_term_id = $tour_term->term_id;
    }
    
    if (!$luxury_term) {
        $luxury_term = wp_insert_term('Luxury Tours', 'ap_product_cat');
        if (!is_wp_error($luxury_term)) {
            $luxury_term_id = $luxury_term['term_id'];
        }
    } else {
        $luxury_term_id = $luxury_term->term_id;
    }
    
    if (!$safari_term) {
        $safari_term = wp_insert_term('Safari Tours', 'ap_product_cat');
        if (!is_wp_error($safari_term)) {
            $safari_term_id = $safari_term['term_id'];
        }
    } else {
        $safari_term_id = $safari_term->term_id;
    }
    
    $term_ids = array();
    if (isset($tour_term_id)) $term_ids[] = $tour_term_id;
    if (isset($luxury_term_id)) $term_ids[] = $luxury_term_id;
    if (isset($safari_term_id)) $term_ids[] = $safari_term_id;
    
    if (!empty($term_ids)) {
        wp_set_post_terms($post_id, $term_ids, 'ap_product_cat');
        echo "Added to Tours, Luxury Tours, and Safari Tours categories\n";
    }
} else {
    echo "Failed to create travel package\n";
}
?>

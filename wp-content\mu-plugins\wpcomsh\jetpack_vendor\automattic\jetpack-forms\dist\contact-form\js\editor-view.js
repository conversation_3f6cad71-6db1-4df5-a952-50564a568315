!function(t,e,n){if(e.mce=e.mce||{},void 0===e.mce.views)return;e.mce.grunion_wp_view_renderer={shortcode_string:"contact-form",template:e.template("grunion-contact-form"),field_templates:{email:e.template("grunion-field-email"),telephone:e.template("grunion-field-telephone"),textarea:e.template("grunion-field-textarea"),radio:e.template("grunion-field-radio"),checkbox:e.template("grunion-field-checkbox"),"checkbox-multiple":e.template("grunion-field-checkbox-multiple"),select:e.template("grunion-field-select"),date:e.template("grunion-field-date"),text:e.template("grunion-field-text"),name:e.template("grunion-field-text"),url:e.template("grunion-field-url")},edit_template:e.template("grunion-field-edit"),editor_inline:e.template("grunion-editor-inline"),editor_option:e.template("grunion-field-edit-option"),getContent:function(){let t,i,o=this.shortcode.content,c=0,r="";for(o||(o=n.default_form);t=e.shortcode.next("contact-field",o,c);)c=t.index+t.content.length,i=t.shortcode.attrs.named,i.type&&this.field_templates[i.type]||(i.type="text"),i.required&&(i.required=n.labels.required_field_text),i.options&&"string"==typeof i.options&&(i.options=i.options.split(",")),r+=this.field_templates[i.type](i);const s={body:r,submit_button_text:n.labels.submit_button_text};return this.template(s)},edit:function(i,o){let c,r,s=e.shortcode.next(this.shortcode_string,i).shortcode,l=t(tinyMCE.activeEditor.getDoc()).find(".wpview.wpview-wrap").filter((function(){return t(this).attr("data-mce-selected")})),a=t('<iframe scrolling="no" class="inline-edit-contact-form" />'),d=0,f="";for(s.content||(s.content=n.default_form);r=e.shortcode.next("contact-field",s.content,d);)d=r.index+r.content.length,c=r.shortcode.attrs.named,c.options&&"string"==typeof c.options&&(c.options=c.options.split(",")),f+=this.edit_template(c);a.on("checkheight",(function(){const t=this.contentDocument?this.contentDocument:this.contentWindow.document;this.style.height="10px",this.style.height=5+t.body.scrollHeight+"px",tinyMCE.activeEditor.execCommand("wpAutoResize")})),a.on("load",(function(){const i=1===window.isRtl?n.inline_editing_style_rtl:n.inline_editing_style,c=t('<link rel="stylesheet" href="'+i+'" />'),r=t('<link rel="stylesheet" href="'+n.dashicons_css_url+'" />');c.on("load",(function(){a.contents().find("body").css("visibility","visible"),a.trigger("checkheight")})),a.contents().find("head").append(c).append(r),a.contents().find("body").html(e.mce.grunion_wp_view_renderer.editor_inline({to:s.attrs.named.to,subject:s.attrs.named.subject,fields:f})).css("visibility","hidden"),a.contents().find("input:first").focus(),setTimeout((function(){a.trigger("checkheight")}),250),setTimeout((function(){a.trigger("checkheight")}),500);const l=a.contents().find(".grunion-fields"),d=a.contents().find(".grunion-controls");l.sortable(),l.on("change select","select[name=type]",(function(){t(this).closest(".grunion-field-edit")[0].className="card is-compact grunion-field-edit grunion-field-"+t(this).val(),a.trigger("checkheight")})),l.on("click",".delete-option",(function(e){e.preventDefault(),t(this).closest("li").remove(),a.trigger("checkheight")})),l.on("click",".add-option",(function(n){const i=t(e.mce.grunion_wp_view_renderer.editor_option());n.preventDefault(),t(this).closest("li").before(i),a.trigger("checkheight"),i.find("input:first").focus()})),l.on("click",".delete-field",(function(e){e.preventDefault(),t(this).closest(".card").remove(),a.trigger("checkheight")})),d.find("input[name=submit]").on("click",(function(){const n=s;n.type="closed",n.attrs={},n.content="",l.children().each((function(){const i={tag:"contact-field",type:"single",attrs:{label:t(this).find("input[name=label]").val(),type:t(this).find("select[name=type]").val()}},o=[];t(this).find("input[name=required]:checked").length&&(i.attrs.required="1"),t(this).find("input[name=option]").each((function(){t(this).val()&&o.push(t(this).val())})),o.length&&(i.attrs.options=o.join(",")),n.content+=e.shortcode.string(i)})),a.contents().find("input[name=to]").val()&&(n.attrs.to=a.contents().find("input[name=to]").val()),a.contents().find("input[name=subject]").val()&&(n.attrs.subject=a.contents().find("input[name=subject]").val()),o(e.shortcode.string(n))})),d.find("input[name=cancel]").on("click",(function(){o(e.shortcode.string(s))})),d.find("input[name=add-field]").on("click",(function(){const n=t(e.mce.grunion_wp_view_renderer.edit_template({}));l.append(n),l.sortable("refresh"),a.trigger("checkheight"),n.find("input:first").focus()}))})),l.html(a)}},e.mce.views.register("contact-form",e.mce.grunion_wp_view_renderer),QTags.addButton("grunion_shortcode",n.labels.quicktags_label,(function(){QTags.insertContent("[contact-form]"+n.default_form+"[/contact-form]")}));const i=t("#wp-content-wrap");t("#insert-jetpack-contact-form").on("click",(function(t){t.preventDefault(),i.hasClass("tmce-active")?tinyMCE.execCommand("grunion_add_form"):i.hasClass("html-active")?QTags.insertContent("[contact-form]"+n.default_form+"[/contact-form]"):window.console.error("Neither TinyMCE nor QuickTags is active. Unable to insert form.")}))}(jQuery,wp,grunionEditorView);
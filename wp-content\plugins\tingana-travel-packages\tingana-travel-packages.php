<?php
/**
 * Plugin Name: Tingana Travel Packages
 * Description: Creates travel packages from itinerary data
 * Version: 1.0
 * Author: Tingana Global Travel
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class TinganaravelPackages {

    public function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('wp_ajax_create_travel_packages', array($this, 'create_travel_packages'));
    }

    public function add_admin_menu() {
        add_management_page(
            'Create Travel Packages',
            'Travel Packages',
            'manage_options',
            'tingana-travel-packages',
            array($this, 'admin_page')
        );
    }

    public function admin_page() {
        ?>
        <div class="wrap">
            <h1>Create Travel Packages</h1>
            <p>Click the button below to create all travel packages from the itinerary data.</p>

            <button id="create-packages" class="button button-primary">Create All Travel Packages</button>

            <div id="package-results" style="margin-top: 20px;"></div>

            <script>
            jQuery(document).ready(function($) {
                $('#create-packages').click(function() {
                    var button = $(this);
                    button.prop('disabled', true).text('Creating packages...');

                    $.ajax({
                        url: ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'create_travel_packages',
                            nonce: '<?php echo wp_create_nonce('create_packages_nonce'); ?>'
                        },
                        success: function(response) {
                            $('#package-results').html('<div class="notice notice-success"><p>' + response + '</p></div>');
                            button.prop('disabled', false).text('Create All Travel Packages');
                        },
                        error: function() {
                            $('#package-results').html('<div class="notice notice-error"><p>Error creating packages.</p></div>');
                            button.prop('disabled', false).text('Create All Travel Packages');
                        }
                    });
                });
            });
            </script>
        </div>
        <?php
    }

    public function create_travel_packages() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'create_packages_nonce')) {
            wp_die('Security check failed');
        }

        $results = array();

        // Package 1: South African Adventure – Child Friendly and Fabulous
        $package1_content = $this->get_package1_content();
        $post1_data = array(
            'post_title' => 'South African Adventure – Child Friendly and Fabulous',
            'post_content' => $package1_content,
            'post_status' => 'publish',
            'post_type' => 'ap_product',
            'post_author' => get_current_user_id(),
            'meta_input' => array(
                'ap_price' => '4999',
                'ap_product_type' => 'tour',
                'duration' => '12 Days',
                'price_adult' => '$4999',
                'price_child' => '$3500',
                'accommodation_level' => '4*-5*',
                'starting_location' => 'Dubai',
                'ending_location' => 'Dubai'
            )
        );

        $post1_id = wp_insert_post($post1_data);
        if ($post1_id) {
            $results[] = "Created: South African Adventure – Child Friendly and Fabulous (ID: $post1_id)";
            $this->add_to_category($post1_id, 'Tours');
        }

        // Package 2: South African Luxury Experience
        $package2_content = $this->get_package2_content();
        $post2_data = array(
            'post_title' => 'South African Luxury Experience',
            'post_content' => $package2_content,
            'post_status' => 'publish',
            'post_type' => 'ap_product',
            'post_author' => get_current_user_id(),
            'meta_input' => array(
                'ap_price' => '0',
                'ap_price_contact' => 'yes',
                'ap_product_type' => 'luxury_tour',
                'duration' => '12 Days',
                'accommodation_level' => '5*',
                'starting_location' => 'Dubai',
                'ending_location' => 'Dubai'
            )
        );

        $post2_id = wp_insert_post($post2_data);
        if ($post2_id) {
            $results[] = "Created: South African Luxury Experience (ID: $post2_id)";
            $this->add_to_category($post2_id, 'Luxury Tours');
        }

        // Package 3: South African Classic Tour
        $package3_content = $this->get_package3_content();
        $post3_data = array(
            'post_title' => 'South African Classic Tour',
            'post_content' => $package3_content,
            'post_status' => 'publish',
            'post_type' => 'ap_product',
            'post_author' => get_current_user_id(),
            'meta_input' => array(
                'ap_price' => '1950',
                'ap_product_type' => 'tour',
                'duration' => '9 Days',
                'price_adult' => '$1950',
                'price_child' => '$1550',
                'accommodation_level' => '4*',
                'starting_location' => 'Johannesburg',
                'ending_location' => 'Cape Town'
            )
        );

        $post3_id = wp_insert_post($post3_data);
        if ($post3_id) {
            $results[] = "Created: South African Classic Tour (ID: $post3_id)";
            $this->add_to_category($post3_id, 'Classic Tours');
        }

        // Package 4: Garden Route & Safari Experience
        $package4_content = $this->get_package4_content();
        $post4_data = array(
            'post_title' => 'Garden Route & Safari Experience',
            'post_content' => $package4_content,
            'post_status' => 'publish',
            'post_type' => 'ap_product',
            'post_author' => get_current_user_id(),
            'meta_input' => array(
                'ap_price' => '0',
                'ap_price_contact' => 'yes',
                'ap_product_type' => 'luxury_tour',
                'duration' => '13 Days',
                'accommodation_level' => '5*',
                'starting_location' => 'Johannesburg',
                'ending_location' => 'Johannesburg'
            )
        );

        $post4_id = wp_insert_post($post4_data);
        if ($post4_id) {
            $results[] = "Created: Garden Route & Safari Experience (ID: $post4_id)";
            $this->add_to_category($post4_id, 'Safari Tours');
        }

        echo implode('<br>', $results);
        wp_die();
    }

    private function add_to_category($post_id, $category_name) {
        $term = get_term_by('name', $category_name, 'ap_product_cat');
        if (!$term) {
            $term = wp_insert_term($category_name, 'ap_product_cat');
            if (!is_wp_error($term)) {
                $term_id = $term['term_id'];
            }
        } else {
            $term_id = $term->term_id;
        }

        if (isset($term_id)) {
            wp_set_post_terms($post_id, array($term_id), 'ap_product_cat');
        }
    }

    private function get_package1_content() {
        return '<h2>Overview</h2>
<p>Experience the magic of South Africa with your family on this specially designed 12-day adventure that combines wildlife encounters, cultural experiences, and child-friendly activities. From the bustling city of Johannesburg to the wildlife-rich Kruger National Park, the stunning Cape Peninsula, and the scenic Garden Route, this tour offers unforgettable memories for the whole family.</p>

<h2>Tour Highlights</h2>
<ul>
<li>Child-friendly activities throughout</li>
<li>VIP Safari experiences in Kruger</li>
<li>Cape Town penguin colony visit</li>
<li>Garden Route whale watching</li>
<li>Luxury accommodations</li>
<li>All meals and transfers included</li>
</ul>

<h2>Detailed Itinerary</h2>
<h3>Day 1: Dubai to Johannesburg</h3>
<p><strong>Accommodation:</strong> Sandton City Hotel (4*-5*)</p>
<p><strong>Activities:</strong> Emirates flight: Dubai to Johannesburg, Private airport transfer with child-friendly welcome packs</p>
<p><strong>Tingana Tip:</strong> Ensure a smooth arrival with dedicated family services</p>

<h3>Day 2: Johannesburg</h3>
<p><strong>Accommodation:</strong> Sandton City Hotel</p>
<p><strong>Activities:</strong> Acrobranch Treetop Adventure, Farm-to-Fork Experience Long Table Lunch</p>
<p><strong>Tingana Tip:</strong> Opt for an interactive farm animal encounter for added excitement</p>

<h2>What\'s Included</h2>
<ul>
<li>11 nights accommodation</li>
<li>11 breakfasts, 10 lunches, 5 dinners</li>
<li>Private transfers</li>
<li>Domestic flights</li>
<li>All tours & activities mentioned</li>
</ul>

<h2>Pricing</h2>
<p><strong>From $4999 per adult | $3500 per child</strong></p>
<p><em>Based on family sharing accommodation (Trade Partner Rates)</em></p>';
    }

    private function get_package2_content() {
        return '<h2>Overview</h2>
<p>Indulge in the ultimate South African luxury experience with this meticulously crafted 12-day journey. From hot air balloon safaris over the Magaliesberg to private yacht experiences in Cape Town, every moment is designed to exceed expectations. Stay in the finest 5-star accommodations while exploring South Africa\'s most iconic destinations with exclusive access and personalized service.</p>

<h2>Tour Highlights</h2>
<ul>
<li>Luxury 5-star accommodations throughout</li>
<li>Bill Harrop\'s Balloon Safari</li>
<li>VIP conservation talks with rangers</li>
<li>Private yacht experiences in Cape Town</li>
<li>Exclusive wine blending experiences</li>
<li>Gourmet dining and culinary tours</li>
</ul>

<h2>Pricing</h2>
<p><strong>Contact us for exclusive pricing</strong></p>
<p><em>This luxury experience is tailored to your preferences with premium accommodations and exclusive access.</em></p>';
    }

    private function get_package3_content() {
        return '<h2>Overview</h2>
<p>Discover the essence of South Africa on this classic 9-day tour that showcases the country\'s most iconic destinations. From the wildlife-rich Kruger National Park to the stunning landscapes of the Cape Peninsula, this carefully crafted itinerary offers the perfect introduction to South Africa\'s natural beauty, rich culture, and warm hospitality.</p>

<h2>Tour Highlights</h2>
<ul>
<li>Kruger National Park safari adventure</li>
<li>Panorama Route scenic drive</li>
<li>Cape Peninsula coastal tour</li>
<li>Table Mountain aerial cableway</li>
<li>Cape Winelands wine tasting</li>
<li>V&A Waterfront shopping and dining</li>
</ul>

<h2>Pricing</h2>
<p><strong>From $1950 per adult | $1550 per child</strong></p>
<p><em>Prices are per person based on double occupancy</em></p>';
    }

    private function get_package4_content() {
        return '<h2>Overview</h2>
<p>Experience the best of South Africa on this comprehensive 13-day journey that combines the scenic beauty of the Garden Route with thrilling safari adventures in Kruger National Park. Stay in luxury 5-star accommodations while exploring dramatic coastlines, charming towns, vibrant cities, and world-renowned wildlife destinations.</p>

<h2>Tour Highlights</h2>
<ul>
<li>Luxury 5-star accommodations throughout</li>
<li>Knysna Heads and Featherbed Nature Reserve</li>
<li>Tsitsikamma National Park adventures</li>
<li>Hermanus whale watching (seasonal)</li>
<li>Cape Town city exploration</li>
<li>Kruger National Park Big Five safari</li>
<li>Sandton shopping and dining</li>
</ul>

<h2>Pricing</h2>
<p><strong>Contact us for personalized pricing</strong></p>
<p><em>This premium experience includes luxury accommodations and can be customized to your preferences.</em></p>';
    }
}

new TinganaravelPackages();
?>

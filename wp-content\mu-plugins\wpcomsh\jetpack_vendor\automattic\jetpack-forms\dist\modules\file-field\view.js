import*as e from"@wordpress/interactivity";var t={889:(e,t,n)=>{n.d(t,{S_:()=>r});const{__:__}=wp.i18n,r=(__("Warning.","jetpack-forms"),(e,t)=>{e.removeAttribute("aria-invalid"),e.removeAttribute("aria-describedby");const n=e.closest(t.hasInsetLabel?".contact-form__inset-label-wrap":".grunion-field-wrap");if(!n)return;const r=n.querySelector(".contact-form__input-error");r&&r.remove();const i=e.closest("form"),o=i.querySelectorAll(".contact-form__input-error"),a=i.querySelector(".contact-form__error");a&&0===o.length&&a.remove()})},833:(t,n,r)=>{t.exports=(e=>{var t={};return r.d(t,e),t})({getConfig:()=>e.getConfig,getContext:()=>e.getContext,getElement:()=>e.getElement,store:()=>e.store,withScope:()=>e.withScope})}},n={};function r(e){var i=n[e];if(void 0!==i)return i.exports;var o=n[e]={exports:{}};return t[e](o,o.exports,r),o.exports}r.d=(e,t)=>{for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);var i=r(833),o=r(889);const a="jetpack/field-file";let s=null,l=null;const c=async()=>{if(s&&l&&Date.now()<l)return s;const{token:e,expiresAt:t}=await d();return s=e,l=1e3*t,s},d=async()=>{const{endpoint:e}=(0,i.getConfig)(a),t={token:null,expiresAt:0};try{const n=await fetch(`${e}/token`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({context:"file-upload"})});if(!n.ok)return t;const r=await n.json();return{token:r.token,expiresAt:r.expiration}}catch(e){if(e)return t}return t},f=(e,t=2)=>{const n=(0,i.getConfig)(a);if(0===e)return n.i18n.zeroBytes;const r=t<0?0:t,o=n.i18n.fileSizeUnits||["Bytes","KB","MB","GB","TB"],s=Math.floor(Math.log(e)/Math.log(1024)),l=parseFloat((e/Math.pow(1024,s)).toFixed(r));return`${new Intl.NumberFormat(n.i18n.locale,{minimumFractionDigits:r,maximumFractionDigits:r}).format(l)} ${o[s]}`},p=e=>{const{ref:t}=(0,i.getElement)();(0,o.S_)(t,{hasInsetLabel:y.isInlineForm});const n=(0,i.getConfig)(a),r=(0,i.getContext)();let s=null;e.size>n.maxUploadSize&&(s=n.i18n.fileTooLarge),r.allowedMimeTypes.includes(e.type)||(s=n.i18n.invalidType);const l=r.files.filter((e=>!e.error));r.maxFiles<l.length+1&&(s=n.i18n.maxFiles);const c=performance.now()+"-"+Math.random(),d=["image/gif","image/jpg","image/png","image/jpeg"].includes(e.type)&&URL.createObjectURL?"url("+URL.createObjectURL(e)+")":null;r.files.push({name:e.name,formattedSize:f(e.size,2),isUploaded:!1,hasError:!!s,id:c,url:d,error:s}),!s&&x.uploadFile(e,c)},g=new Map,m=(e,t)=>{const n=t.loaded/t.total*100;h({progress:Math.min(n,97)},e)},u=(e,t)=>{const n=t.target;if(4===n.readyState){if(200!==n.status){const t=(0,i.getConfig)(a);return void h({error:t.i18n.uploadFailed,hasError:!0},e)}{const t=JSON.parse(n.responseText);if(t.success)return void h({file_id:t.data.file_id,isUploaded:!0,name:t.data.name,type:t.data.type,size:t.data.size,fileJson:JSON.stringify({file_id:t.data.file_id,name:t.data.name,size:t.data.size,type:t.data.type})},e)}if(n.responseText){const t=JSON.parse(n.responseText);h({error:t.message,hasError:!0},e)}}},h=(e,t)=>{const n=(0,i.getContext)(),r=n.files.findIndex((e=>e.id===t));n.files[r]=Object.assign(n.files[r],e)},{state:y,actions:x}=(0,i.store)(a,{state:{get isInlineForm(){const{ref:e}=(0,i.getElement)(),t=e.closest(".wp-block-jetpack-contact-form");return t&&t.classList.contains("is-style-outlined")||t.classList.contains("is-style-animated")},get hasFiles(){return!!(0,i.getContext)().files.length>0},get hasMaxFiles(){const e=(0,i.getContext)();return e.maxFiles<=e.files.length}},actions:{openFilePicker(){const{ref:e}=(0,i.getElement)(),t=e.parentNode.querySelector(".jetpack-form-file-field");t&&(t.value="",t.click())},fileAdded(e){Array.from(e.target.files).forEach(p)},fileDropped:e=>{if(e.preventDefault(),e.dataTransfer)for(const t of Array.from(e.dataTransfer.items)){if(t.webkitGetAsEntry()?.isDirectory)return;p(t.getAsFile())}(0,i.getContext)().isDropping=!1},dragOver:e=>{(0,i.getContext)().isDropping=!0,e.preventDefault()},dragLeave:()=>{(0,i.getContext)().isDropping=!1},uploadFile:function*(e,t){const{endpoint:n,i18n:r}=(0,i.getConfig)(a),o=yield c();if(!o)return void h({error:r.uploadFailed,hasError:!0},t);const s=new XMLHttpRequest,l=new FormData,d=new AbortController;g.set(t,d),s.open("POST",n,!0),s.upload.addEventListener("progress",(0,i.withScope)(m.bind(this,t))),s.addEventListener("readystatechange",(0,i.withScope)(u.bind(this,t))),d.signal.addEventListener("abort",(()=>{s.abort()})),l.append("file",e),l.append("token",o),s.send(l)},removeFile:function*(e){e.preventDefault();const{ref:t}=(0,i.getElement)(),n=t.closest(".jetpack-form-file-field__container");(0,o.S_)(n,{hasInsetLabel:y.isInlineForm});const r=(0,i.getContext)(),s=e.target.dataset.id;if(g.has(s)){g.get(s).abort(),g.delete(s)}const l=r.files.find((e=>e.id===s));if(l&&l.url){const e=l.url.substring(4,l.url.length-1);URL.revokeObjectURL(e)}if(l&&l.file_id){const{endpoint:e}=(0,i.getConfig)(a),t=yield c();if(t){const n=new FormData;n.append("token",t),n.append("file_id",l.file_id),fetch(`${e}/remove`,{method:"POST",body:n})}}r.files=r.files.filter((e=>e.id!==s))}},callbacks:{}});
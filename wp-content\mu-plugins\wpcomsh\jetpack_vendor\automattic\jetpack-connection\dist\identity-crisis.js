(()=>{var e={2144:()=>{},2145:()=>{},9958:()=>{},9626:()=>{},4804:(e,t,n)=>{t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;const n="color: "+this.color;t.splice(1,0,n,"color: inherit");let r=0,s=0;t[0].replace(/%[a-zA-Z%]/g,(e=>{"%%"!==e&&(r++,"%c"===e&&(s=r))})),t.splice(s,0,n)},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch(e){}},t.load=function(){let e;try{e=t.storage.getItem("debug")}catch(e){}!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG);return e},t.useColors=function(){if("undefined"!=typeof window&&window.process&&("renderer"===window.process.type||window.process.__nwjs))return!0;if("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;let e;return"undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)},t.storage=function(){try{return localStorage}catch(e){}}(),t.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.log=console.debug||console.log||(()=>{}),e.exports=n(5067)(t);const{formatters:r}=e.exports;r.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},5067:(e,t,n)=>{e.exports=function(e){function t(e){let n,s,a,o=null;function c(...e){if(!c.enabled)return;const r=c,s=Number(new Date),a=s-(n||s);r.diff=a,r.prev=n,r.curr=s,n=s,e[0]=t.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let o=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,((n,s)=>{if("%%"===n)return"%";o++;const a=t.formatters[s];if("function"==typeof a){const t=e[o];n=a.call(r,t),e.splice(o,1),o--}return n})),t.formatArgs.call(r,e);(r.log||t.log).apply(r,e)}return c.namespace=e,c.useColors=t.useColors(),c.color=t.selectColor(e),c.extend=r,c.destroy=t.destroy,Object.defineProperty(c,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==o?o:(s!==t.namespaces&&(s=t.namespaces,a=t.enabled(e)),a),set:e=>{o=e}}),"function"==typeof t.init&&t.init(c),c}function r(e,n){const r=t(this.namespace+(void 0===n?":":n)+e);return r.log=this.log,r}function s(e,t){let n=0,r=0,s=-1,a=0;for(;n<e.length;)if(r<t.length&&(t[r]===e[n]||"*"===t[r]))"*"===t[r]?(s=r,a=n,r++):(n++,r++);else{if(-1===s)return!1;r=s+1,a++,n=a}for(;r<t.length&&"*"===t[r];)r++;return r===t.length}return t.debug=t,t.default=t,t.coerce=function(e){if(e instanceof Error)return e.stack||e.message;return e},t.disable=function(){const e=[...t.names,...t.skips.map((e=>"-"+e))].join(",");return t.enable(""),e},t.enable=function(e){t.save(e),t.namespaces=e,t.names=[],t.skips=[];const n=("string"==typeof e?e:"").trim().replace(" ",",").split(",").filter(Boolean);for(const e of n)"-"===e[0]?t.skips.push(e.slice(1)):t.names.push(e)},t.enabled=function(e){for(const n of t.skips)if(s(e,n))return!1;for(const n of t.names)if(s(e,n))return!0;return!1},t.humanize=n(3594),t.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach((n=>{t[n]=e[n]})),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let n=0;for(let t=0;t<e.length;t++)n=(n<<5)-n+e.charCodeAt(t),n|=0;return t.colors[Math.abs(n)%t.colors.length]},t.enable(t.load()),t}},3594:e=>{var t=1e3,n=60*t,r=60*n,s=24*r,a=7*s,o=365.25*s;function c(e,t,n,r){var s=t>=1.5*n;return Math.round(e/n)+" "+r+(s?"s":"")}e.exports=function(e,i){i=i||{};var l=typeof e;if("string"===l&&e.length>0)return function(e){if((e=String(e)).length>100)return;var c=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(!c)return;var i=parseFloat(c[1]);switch((c[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return i*o;case"weeks":case"week":case"w":return i*a;case"days":case"day":case"d":return i*s;case"hours":case"hour":case"hrs":case"hr":case"h":return i*r;case"minutes":case"minute":case"mins":case"min":case"m":return i*n;case"seconds":case"second":case"secs":case"sec":case"s":return i*t;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return i;default:return}}(e);if("number"===l&&isFinite(e))return i.long?function(e){var a=Math.abs(e);if(a>=s)return c(e,a,s,"day");if(a>=r)return c(e,a,r,"hour");if(a>=n)return c(e,a,n,"minute");if(a>=t)return c(e,a,t,"second");return e+" ms"}(e):function(e){var a=Math.abs(e);if(a>=s)return Math.round(e/s)+"d";if(a>=r)return Math.round(e/r)+"h";if(a>=n)return Math.round(e/n)+"m";if(a>=t)return Math.round(e/t)+"s";return e+"ms"}(e);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},1583:(e,t,n)=>{"use strict";var r=n(1752);function s(){}function a(){}a.resetWarningCache=s,e.exports=function(){function e(e,t,n,s,a,o){if(o!==r){var c=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw c.name="Invariant Violation",c}}function t(){return e}e.isRequired=e;var n={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:a,resetWarningCache:s};return n.PropTypes=n,n}},3619:(e,t,n)=>{e.exports=n(1583)()},1752:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},372:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var r=n(4804);const s=n.n(r)()("dops:analytics");let a,o;window._tkq=window._tkq||[],window.ga=window.ga||function(){(window.ga.q=window.ga.q||[]).push(arguments)},window.ga.l=+new Date;const c={initialize:function(e,t,n){c.setUser(e,t),c.setSuperProps(n),c.identifyUser()},setGoogleAnalyticsEnabled:function(e,t=null){this.googleAnalyticsEnabled=e,this.googleAnalyticsKey=t},setMcAnalyticsEnabled:function(e){this.mcAnalyticsEnabled=e},setUser:function(e,t){o={ID:e,username:t}},setSuperProps:function(e){a=e},assignSuperProps:function(e){a=Object.assign(a||{},e)},mc:{bumpStat:function(e,t){const n=function(e,t){let n="";if("object"==typeof e){for(const t in e)n+="&x_"+encodeURIComponent(t)+"="+encodeURIComponent(e[t]);s("Bumping stats %o",e)}else n="&x_"+encodeURIComponent(e)+"="+encodeURIComponent(t),s('Bumping stat "%s" in group "%s"',t,e);return n}(e,t);c.mcAnalyticsEnabled&&((new Image).src=document.location.protocol+"//pixel.wp.com/g.gif?v=wpcom-no-pv"+n+"&t="+Math.random())},bumpStatWithPageView:function(e,t){const n=function(e,t){let n="";if("object"==typeof e){for(const t in e)n+="&"+encodeURIComponent(t)+"="+encodeURIComponent(e[t]);s("Built stats %o",e)}else n="&"+encodeURIComponent(e)+"="+encodeURIComponent(t),s('Built stat "%s" in group "%s"',t,e);return n}(e,t);c.mcAnalyticsEnabled&&((new Image).src=document.location.protocol+"//pixel.wp.com/g.gif?v=wpcom"+n+"&t="+Math.random())}},pageView:{record:function(e,t){c.tracks.recordPageView(e),c.ga.recordPageView(e,t)}},purchase:{record:function(e,t,n,r,s,a,o){c.ga.recordPurchase(e,t,n,r,s,a,o)}},tracks:{recordEvent:function(e,t){t=t||{},0===e.indexOf("akismet_")||0===e.indexOf("jetpack_")?(a&&(s("- Super Props: %o",a),t=Object.assign(t,a)),s('Record event "%s" called with props %s',e,JSON.stringify(t)),window._tkq.push(["recordEvent",e,t])):s('- Event name must be prefixed by "akismet_" or "jetpack_"')},recordJetpackClick:function(e){const t="object"==typeof e?e:{target:e};c.tracks.recordEvent("jetpack_wpa_click",t)},recordPageView:function(e){c.tracks.recordEvent("akismet_page_view",{path:e})},setOptOut:function(e){s("Pushing setOptOut: %o",e),window._tkq.push(["setOptOut",e])}},ga:{initialized:!1,initialize:function(){let e={};c.ga.initialized||(o&&(e={userId:"u-"+o.ID}),window.ga("create",this.googleAnalyticsKey,"auto",e),c.ga.initialized=!0)},recordPageView:function(e,t){c.ga.initialize(),s("Recording Page View ~ [URL: "+e+"] [Title: "+t+"]"),this.googleAnalyticsEnabled&&(window.ga("set","page",e),window.ga("send",{hitType:"pageview",page:e,title:t}))},recordEvent:function(e,t,n,r){c.ga.initialize();let a="Recording Event ~ [Category: "+e+"] [Action: "+t+"]";void 0!==n&&(a+=" [Option Label: "+n+"]"),void 0!==r&&(a+=" [Option Value: "+r+"]"),s(a),this.googleAnalyticsEnabled&&window.ga("send","event",e,t,n,r)},recordPurchase:function(e,t,n,r,s,a,o){window.ga("require","ecommerce"),window.ga("ecommerce:addTransaction",{id:e,revenue:r,currency:o}),window.ga("ecommerce:addItem",{id:e,name:t,sku:n,price:s,quantity:a}),window.ga("ecommerce:send")}},identifyUser:function(){o&&window._tkq.push(["identifyUser",o.ID,o.username])},setProperties:function(e){window._tkq.push(["setProperties",e])},clearedIdentity:function(){window._tkq.push(["clearIdentity"])}},i=c},5932:(e,t,n)=>{"use strict";n.d(t,{Ay:()=>p});var r=n(6439),s=n(3832);function a(e){class t extends Error{constructor(...t){super(...t),this.name=e}}return t}const o=a("JsonParseError"),c=a("JsonParseAfterRedirectError"),i=a("Api404Error"),l=a("Api404AfterRedirectError"),d=a("FetchNetworkError");const p=new function(e,t){let n=e,a=e,o={"X-WP-Nonce":t},c={credentials:"same-origin",headers:o},i={method:"post",credentials:"same-origin",headers:Object.assign({},o,{"Content-type":"application/json"})},l=function(e){const t=e.split("?"),n=t.length>1?t[1]:"",r=n.length?n.split("&"):[];return r.push("_cacheBuster="+(new Date).getTime()),t[0]+"?"+r.join("&")};const d={setApiRoot(e){n=e},setWpcomOriginApiUrl(e){a=e},setApiNonce(e){o={"X-WP-Nonce":e},c={credentials:"same-origin",headers:o},i={method:"post",credentials:"same-origin",headers:Object.assign({},o,{"Content-type":"application/json"})}},setCacheBusterCallback:e=>{l=e},registerSite:(e,t,s)=>{const a={};return(0,r.jetpackConfigHas)("consumer_slug")&&(a.plugin_slug=(0,r.jetpackConfigGet)("consumer_slug")),null!==t&&(a.redirect_uri=t),s&&(a.from=s),h(`${n}jetpack/v4/connection/register`,i,{body:JSON.stringify(a)}).then(m).then(u)},fetchAuthorizationUrl:e=>p((0,s.addQueryArgs)(`${n}jetpack/v4/connection/authorize_url`,{no_iframe:"1",redirect_uri:e}),c).then(m).then(u),fetchSiteConnectionData:()=>p(`${n}jetpack/v4/connection/data`,c).then(u),fetchSiteConnectionStatus:()=>p(`${n}jetpack/v4/connection`,c).then(u),fetchSiteConnectionTest:()=>p(`${n}jetpack/v4/connection/test`,c).then(m).then(u),fetchUserConnectionData:()=>p(`${n}jetpack/v4/connection/data`,c).then(u),fetchUserTrackingSettings:()=>p(`${n}jetpack/v4/tracking/settings`,c).then(m).then(u),updateUserTrackingSettings:e=>h(`${n}jetpack/v4/tracking/settings`,i,{body:JSON.stringify(e)}).then(m).then(u),disconnectSite:()=>h(`${n}jetpack/v4/connection`,i,{body:JSON.stringify({isActive:!1})}).then(m).then(u),fetchConnectUrl:()=>p(`${n}jetpack/v4/connection/url`,c).then(m).then(u),unlinkUser:(e=!1,t={})=>{const r={linked:!1,force:!!e};return t.disconnectAllUsers&&(r["disconnect-all-users"]=!0),h(`${n}jetpack/v4/connection/user`,i,{body:JSON.stringify(r)}).then(m).then(u)},reconnect:()=>h(`${n}jetpack/v4/connection/reconnect`,i).then(m).then(u),fetchConnectedPlugins:()=>p(`${n}jetpack/v4/connection/plugins`,c).then(m).then(u),setHasSeenWCConnectionModal:()=>h(`${n}jetpack/v4/seen-wc-connection-modal`,i).then(m).then(u),fetchModules:()=>p(`${n}jetpack/v4/module/all`,c).then(m).then(u),fetchModule:e=>p(`${n}jetpack/v4/module/${e}`,c).then(m).then(u),activateModule:e=>h(`${n}jetpack/v4/module/${e}/active`,i,{body:JSON.stringify({active:!0})}).then(m).then(u),deactivateModule:e=>h(`${n}jetpack/v4/module/${e}/active`,i,{body:JSON.stringify({active:!1})}),updateModuleOptions:(e,t)=>h(`${n}jetpack/v4/module/${e}`,i,{body:JSON.stringify(t)}).then(m).then(u),updateSettings:e=>h(`${n}jetpack/v4/settings`,i,{body:JSON.stringify(e)}).then(m).then(u),getProtectCount:()=>p(`${n}jetpack/v4/module/protect/data`,c).then(m).then(u),resetOptions:e=>h(`${n}jetpack/v4/options/${e}`,i,{body:JSON.stringify({reset:!0})}).then(m).then(u),activateVaultPress:()=>h(`${n}jetpack/v4/plugins`,i,{body:JSON.stringify({slug:"vaultpress",status:"active"})}).then(m).then(u),getVaultPressData:()=>p(`${n}jetpack/v4/module/vaultpress/data`,c).then(m).then(u),installPlugin:(e,t)=>{const r={slug:e,status:"active"};return t&&(r.source=t),h(`${n}jetpack/v4/plugins`,i,{body:JSON.stringify(r)}).then(m).then(u)},activateAkismet:()=>h(`${n}jetpack/v4/plugins`,i,{body:JSON.stringify({slug:"akismet",status:"active"})}).then(m).then(u),getAkismetData:()=>p(`${n}jetpack/v4/module/akismet/data`,c).then(m).then(u),checkAkismetKey:()=>p(`${n}jetpack/v4/module/akismet/key/check`,c).then(m).then(u),checkAkismetKeyTyped:e=>h(`${n}jetpack/v4/module/akismet/key/check`,i,{body:JSON.stringify({api_key:e})}).then(m).then(u),getFeatureTypeStatus:e=>p(`${n}jetpack/v4/feature/${e}`,c).then(m).then(u),fetchStatsData:e=>p(function(e){let t=`${n}jetpack/v4/module/stats/data`;-1!==t.indexOf("?")?t+=`&range=${encodeURIComponent(e)}`:t+=`?range=${encodeURIComponent(e)}`;return t}(e),c).then(m).then(u).then(f),getPluginUpdates:()=>p(`${n}jetpack/v4/updates/plugins`,c).then(m).then(u),getPlans:()=>p(`${n}jetpack/v4/plans`,c).then(m).then(u),fetchSettings:()=>p(`${n}jetpack/v4/settings`,c).then(m).then(u),updateSetting:e=>h(`${n}jetpack/v4/settings`,i,{body:JSON.stringify(e)}).then(m).then(u),fetchSiteData:()=>p(`${n}jetpack/v4/site`,c).then(m).then(u).then((e=>JSON.parse(e.data))),fetchSiteFeatures:()=>p(`${n}jetpack/v4/site/features`,c).then(m).then(u).then((e=>JSON.parse(e.data))),fetchSiteProducts:()=>p(`${n}jetpack/v4/site/products`,c).then(m).then(u),fetchSitePurchases:()=>p(`${n}jetpack/v4/site/purchases`,c).then(m).then(u).then((e=>JSON.parse(e.data))),fetchSiteBenefits:()=>p(`${n}jetpack/v4/site/benefits`,c).then(m).then(u).then((e=>JSON.parse(e.data))),fetchSiteDiscount:()=>p(`${n}jetpack/v4/site/discount`,c).then(m).then(u).then((e=>e.data)),fetchSetupQuestionnaire:()=>p(`${n}jetpack/v4/setup/questionnaire`,c).then(m).then(u),fetchRecommendationsData:()=>p(`${n}jetpack/v4/recommendations/data`,c).then(m).then(u),fetchRecommendationsProductSuggestions:()=>p(`${n}jetpack/v4/recommendations/product-suggestions`,c).then(m).then(u),fetchRecommendationsUpsell:()=>p(`${n}jetpack/v4/recommendations/upsell`,c).then(m).then(u),fetchRecommendationsConditional:()=>p(`${n}jetpack/v4/recommendations/conditional`,c).then(m).then(u),saveRecommendationsData:e=>h(`${n}jetpack/v4/recommendations/data`,i,{body:JSON.stringify({data:e})}).then(m),fetchProducts:()=>p(`${n}jetpack/v4/products`,c).then(m).then(u),fetchRewindStatus:()=>p(`${n}jetpack/v4/rewind`,c).then(m).then(u).then((e=>JSON.parse(e.data))),fetchScanStatus:()=>p(`${n}jetpack/v4/scan`,c).then(m).then(u).then((e=>JSON.parse(e.data))),dismissJetpackNotice:e=>h(`${n}jetpack/v4/notice/${e}`,i,{body:JSON.stringify({dismissed:!0})}).then(m).then(u),fetchPluginsData:()=>p(`${n}jetpack/v4/plugins`,c).then(m).then(u),fetchIntroOffers:()=>p(`${n}jetpack/v4/intro-offers`,c).then(m).then(u),fetchVerifySiteGoogleStatus:e=>p(null!==e?`${n}jetpack/v4/verify-site/google/${e}`:`${n}jetpack/v4/verify-site/google`,c).then(m).then(u),verifySiteGoogle:e=>h(`${n}jetpack/v4/verify-site/google`,i,{body:JSON.stringify({keyring_id:e})}).then(m).then(u),submitSurvey:e=>h(`${n}jetpack/v4/marketing/survey`,i,{body:JSON.stringify(e)}).then(m).then(u),saveSetupQuestionnaire:e=>h(`${n}jetpack/v4/setup/questionnaire`,i,{body:JSON.stringify(e)}).then(m).then(u),updateLicensingError:e=>h(`${n}jetpack/v4/licensing/error`,i,{body:JSON.stringify(e)}).then(m).then(u),updateLicenseKey:e=>h(`${n}jetpack/v4/licensing/set-license`,i,{body:JSON.stringify({license:e})}).then(m).then(u),getUserLicensesCounts:()=>p(`${n}jetpack/v4/licensing/user/counts`,c).then(m).then(u),getUserLicenses:()=>p(`${n}jetpack/v4/licensing/user/licenses`,c).then(m).then(u),updateLicensingActivationNoticeDismiss:e=>h(`${n}jetpack/v4/licensing/user/activation-notice-dismiss`,i,{body:JSON.stringify({last_detached_count:e})}).then(m).then(u),updateRecommendationsStep:e=>h(`${n}jetpack/v4/recommendations/step`,i,{body:JSON.stringify({step:e})}).then(m),confirmIDCSafeMode:()=>h(`${n}jetpack/v4/identity-crisis/confirm-safe-mode`,i).then(m),startIDCFresh:e=>h(`${n}jetpack/v4/identity-crisis/start-fresh`,i,{body:JSON.stringify({redirect_uri:e})}).then(m).then(u),migrateIDC:()=>h(`${n}jetpack/v4/identity-crisis/migrate`,i).then(m),attachLicenses:e=>h(`${n}jetpack/v4/licensing/attach-licenses`,i,{body:JSON.stringify({licenses:e})}).then(m).then(u),fetchSearchPlanInfo:()=>p(`${a}jetpack/v4/search/plan`,c).then(m).then(u),fetchSearchSettings:()=>p(`${a}jetpack/v4/search/settings`,c).then(m).then(u),updateSearchSettings:e=>h(`${a}jetpack/v4/search/settings`,i,{body:JSON.stringify(e)}).then(m).then(u),fetchSearchStats:()=>p(`${a}jetpack/v4/search/stats`,c).then(m).then(u),fetchWafSettings:()=>p(`${n}jetpack/v4/waf`,c).then(m).then(u),updateWafSettings:e=>h(`${n}jetpack/v4/waf`,i,{body:JSON.stringify(e)}).then(m).then(u),fetchWordAdsSettings:()=>p(`${n}jetpack/v4/wordads/settings`,c).then(m).then(u),updateWordAdsSettings:e=>h(`${n}jetpack/v4/wordads/settings`,i,{body:JSON.stringify(e)}),fetchSearchPricing:()=>p(`${a}jetpack/v4/search/pricing`,c).then(m).then(u),fetchMigrationStatus:()=>p(`${n}jetpack/v4/migration/status`,c).then(m).then(u),fetchBackupUndoEvent:()=>p(`${n}jetpack/v4/site/backup/undo-event`,c).then(m).then(u),fetchBackupPreflightStatus:()=>p(`${n}jetpack/v4/site/backup/preflight`,c).then(m).then(u)};function p(e,t){return fetch(l(e),t)}function h(e,t,n){return fetch(e,Object.assign({},t,n)).catch(g)}function f(e){return e.general&&void 0===e.general.response||e.week&&void 0===e.week.response||e.month&&void 0===e.month.response?e:{}}Object.assign(this,d)};function m(e){return e.status>=200&&e.status<300?e:404===e.status?new Promise((()=>{throw e.redirected?new l(e.redirected):new i})):e.json().catch((e=>h(e))).then((t=>{const n=new Error(`${t.message} (Status ${e.status})`);throw n.response=t,n.name="ApiError",n}))}function u(e){return e.json().catch((t=>h(t,e.redirected,e.url)))}function h(e,t,n){throw t?new c(n):new o}function g(){throw new d}},7142:(e,t,n)=>{"use strict";n.d(t,{A:()=>l});var r=n(8579),s=n.n(r),a=n(7723),o=n(2231),c=n(1609),i=n.n(c);const __=a.__,l=({logoColor:e="#069e08",showText:t=!0,className:n,height:r=32,...a})=>{const c=t?"0 0 118 32":"0 0 32 32";return i().createElement("svg",s()({xmlns:"http://www.w3.org/2000/svg",x:"0px",y:"0px",viewBox:c,className:(0,o.A)("jetpack-logo",n),"aria-labelledby":"jetpack-logo-title",height:r},a,{role:"img"}),i().createElement("title",{id:"jetpack-logo-title"},__("Jetpack Logo","jetpack-connection")),i().createElement("path",{fill:e,d:"M16,0C7.2,0,0,7.2,0,16s7.2,16,16,16s16-7.2,16-16S24.8,0,16,0z M15,19H7l8-16V19z M17,29V13h8L17,29z"}),t&&i().createElement(i().Fragment,null,i().createElement("path",{d:"M41.3,26.6c-0.5-0.7-0.9-1.4-1.3-2.1c2.3-1.4,3-2.5,3-4.6V8h-3V6h6v13.4C46,22.8,45,24.8,41.3,26.6z"}),i().createElement("path",{d:"M65,18.4c0,1.1,0.8,1.3,1.4,1.3c0.5,0,2-0.2,2.6-0.4v2.1c-0.9,0.3-2.5,0.5-3.7,0.5c-1.5,0-3.2-0.5-3.2-3.1V12H60v-2h2.1V7.1 H65V10h4v2h-4V18.4z"}),i().createElement("path",{d:"M71,10h3v1.3c1.1-0.8,1.9-1.3,3.3-1.3c2.5,0,4.5,1.8,4.5,5.6s-2.2,6.3-5.8,6.3c-0.9,0-1.3-0.1-2-0.3V28h-3V10z M76.5,12.3 c-0.8,0-1.6,0.4-2.5,1.2v5.9c0.6,0.1,0.9,0.2,1.8,0.2c2,0,3.2-1.3,3.2-3.9C79,13.4,78.1,12.3,76.5,12.3z"}),i().createElement("path",{d:"M93,22h-3v-1.5c-0.9,0.7-1.9,1.5-3.5,1.5c-1.5,0-3.1-1.1-3.1-3.2c0-2.9,2.5-3.4,4.2-3.7l2.4-0.3v-0.3c0-1.5-0.5-2.3-2-2.3 c-0.7,0-2.3,0.5-3.7,1.1L84,11c1.2-0.4,3-1,4.4-1c2.7,0,4.6,1.4,4.6,4.7L93,22z M90,16.4l-2.2,0.4c-0.7,0.1-1.4,0.5-1.4,1.6 c0,0.9,0.5,1.4,1.3,1.4s1.5-0.5,2.3-1V16.4z"}),i().createElement("path",{d:"M104.5,21.3c-1.1,0.4-2.2,0.6-3.5,0.6c-4.2,0-5.9-2.4-5.9-5.9c0-3.7,2.3-6,6.1-6c1.4,0,2.3,0.2,3.2,0.5V13 c-0.8-0.3-2-0.6-3.2-0.6c-1.7,0-3.2,0.9-3.2,3.6c0,2.9,1.5,3.8,3.3,3.8c0.9,0,1.9-0.2,3.2-0.7V21.3z"}),i().createElement("path",{d:"M110,15.2c0.2-0.3,0.2-0.8,3.8-5.2h3.7l-4.6,5.7l5,6.3h-3.7l-4.2-5.8V22h-3V6h3V15.2z"}),i().createElement("path",{d:"M58.5,21.3c-1.5,0.5-2.7,0.6-4.2,0.6c-3.6,0-5.8-1.8-5.8-6c0-3.1,1.9-5.9,5.5-5.9s4.9,2.5,4.9,4.9c0,0.8,0,1.5-0.1,2h-7.3 c0.1,2.5,1.5,2.8,3.6,2.8c1.1,0,2.2-0.3,3.4-0.7C58.5,19,58.5,21.3,58.5,21.3z M56,15c0-1.4-0.5-2.9-2-2.9c-1.4,0-2.3,1.3-2.4,2.9 C51.6,15,56,15,56,15z"})))}},6461:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var r=n(3619),s=n.n(r),a=n(1609),o=n.n(a);n(2144);const c=({color:e="#FFFFFF",className:t="",size:n=20})=>{const r=t+" jp-components-spinner",s={width:n,height:n,fontSize:n,borderTopColor:e},a={borderTopColor:e,borderRightColor:e};return o().createElement("div",{className:r},o().createElement("div",{className:"jp-components-spinner__outer",style:s},o().createElement("div",{className:"jp-components-spinner__inner",style:a})))};c.propTypes={color:s().string,className:s().string,size:s().number};const i=c},3924:(e,t,n)=>{"use strict";function r(e,t={}){const n={};let r;if("undefined"!=typeof window&&(r=window?.JP_CONNECTION_INITIAL_STATE?.calypsoEnv),0===e.search("https://")){const t=new URL(e);e=`https://${t.host}${t.pathname}`,n.url=encodeURIComponent(e)}else n.source=encodeURIComponent(e);for(const e in t)n[e]=encodeURIComponent(t[e]);!Object.keys(n).includes("site")&&"undefined"!=typeof jetpack_redirects&&Object.hasOwn(jetpack_redirects,"currentSiteRawUrl")&&(n.site=jetpack_redirects.currentBlogID??jetpack_redirects.currentSiteRawUrl),r&&(n.calypso_env=r);return"https://jetpack.com/redirect/?"+Object.keys(n).map((e=>e+"="+n[e])).join("&")}n.d(t,{A:()=>r})},6439:(e,t,n)=>{let r={};try{r=n(9074)}catch{console.error("jetpackConfig is missing in your webpack config file. See @automattic/jetpack-config"),r={missingConfig:!0}}const s=e=>Object.hasOwn(r,e);e.exports={jetpackConfigHas:s,jetpackConfigGet:e=>{if(!s(e))throw'This app requires the "'+e+'" Jetpack Config to be defined in your webpack configuration file. See details in @automattic/jetpack-config package docs.';return r[e]}}},3685:(e,t,n)=>{"use strict";n.d(t,{A:()=>y});var r=n(3924),s=n(6461),a=n(6427),o=n(7143),c=n(6087),i=n(7723),l=n(3619),d=n.n(l),p=n(1609),m=n.n(p),u=n(3207),h=n(1057),g=n(6043),f=n(6576);const __=i.__,k=e=>{const{isStartingFresh:t=!1,startFreshCallback:n=()=>{},customContent:l={},hasError:d=!1,isDevelopmentSite:p}=e,h=(0,g.A)(e.wpcomHomeUrl),k=(0,g.A)(e.currentUrl),y=(0,o.useSelect)((e=>e(u.a).getIsActionInProgress()),[]),v=l.startFreshButtonLabel||__("Create a fresh connection","jetpack-connection");return m().createElement("div",{className:"jp-idc__idc-screen__card-action-base"+(d?" jp-idc__idc-screen__card-action-error":"")},m().createElement("div",{className:"jp-idc__idc-screen__card-action-top"},m().createElement("h4",null,l.startFreshCardTitle?(0,c.createInterpolateElement)(l.startFreshCardTitle,{em:m().createElement("em",null)}):__("Treat each site as independent sites","jetpack-connection")),p?m().createElement("div",{className:"jp-idc__dev-mode-content"},(0,c.createInterpolateElement)(l.startFreshCardBodyTextDev||(0,i.sprintf)(/* translators: %1$s: The current site domain name. %2$s: The original site domain name. */
__("<p><strong>Recommended for</strong></p><list><item>development sites</item><item>sites that need access to all Jetpack features</item></list><p><strong>Please note</strong> that creating a fresh connection for <hostname>%1$s</hostname> would require restoring the connection on <hostname>%2$s</hostname> if that site is cloned back to production. <safeModeLink>Learn more</safeModeLink>.</p>","jetpack-connection"),k,h),{p:m().createElement("p",null),hostname:m().createElement("strong",null),em:m().createElement("em",null),strong:m().createElement("strong",null),list:m().createElement("ul",null),item:m().createElement("li",null),safeModeLink:m().createElement("a",{href:l.supportURL||(0,r.A)("jetpack-support-safe-mode"),rel:"noopener noreferrer",target:"_blank"})})):m().createElement("p",null,(0,c.createInterpolateElement)(l.startFreshCardBodyText||(0,i.sprintf)(/* translators: %1$s: The current site domain name. %2$s: The original site domain name. */
__("<hostname>%1$s</hostname> settings, stats, and subscribers will start fresh. <hostname>%2$s</hostname> will keep its data as is.","jetpack-connection"),k,h),{hostname:m().createElement("strong",null),em:m().createElement("em",null),strong:m().createElement("strong",null)}))),m().createElement("div",{className:"jp-idc__idc-screen__card-action-bottom"},m().createElement("div",null,p?null:((e,t)=>m().createElement("div",null,m().createElement("div",{className:"jp-idc__idc-screen__card-action-sitename"},e),m().createElement(a.Dashicon,{icon:"minus",className:"jp-idc__idc-screen__card-action-separator"}),m().createElement("div",{className:"jp-idc__idc-screen__card-action-sitename"},t)))(h,k)),m().createElement(a.Button,{className:"jp-idc__idc-screen__card-action-button",label:v,onClick:n,disabled:y},t?m().createElement(s.A,null):v),d&&(_=l.supportURL,m().createElement(f.A,null,(0,c.createInterpolateElement)(__("Could not create the connection. Retry or find out more <a>here</a>.","jetpack-connection"),{a:m().createElement("a",{href:_||(0,r.A)("jetpack-support-safe-mode"),rel:"noopener noreferrer",target:"_blank"})})))));var _};k.propTypes={wpcomHomeUrl:d().string.isRequired,currentUrl:d().string.isRequired,isStartingFresh:d().bool,startFreshCallback:d().func,customContent:d().shape(h.A),hasError:d().bool,isDevelopmentSite:d().bool};const y=k},6930:(e,t,n)=>{"use strict";n.d(t,{A:()=>y});var r=n(3924),s=n(6461),a=n(6427),o=n(7143),c=n(6087),i=n(7723),l=n(3619),d=n.n(l),p=n(1609),m=n.n(p),u=n(3207),h=n(1057),g=n(6043),f=n(6576);const __=i.__,k=e=>{const t=(0,g.A)(e.wpcomHomeUrl),n=(0,g.A)(e.currentUrl),l=(0,o.useSelect)((e=>e(u.a).getIsActionInProgress()),[]),{isMigrating:d=!1,migrateCallback:p=()=>{},customContent:h={},hasError:k=!1}=e,y=h.migrateButtonLabel||__("Move your settings","jetpack-connection");return m().createElement("div",{className:"jp-idc__idc-screen__card-action-base"+(k?" jp-idc__idc-screen__card-action-error":"")},m().createElement("div",{className:"jp-idc__idc-screen__card-action-top"},m().createElement("h4",null,h.migrateCardTitle?(0,c.createInterpolateElement)(h.migrateCardTitle,{em:m().createElement("em",null)}):__("Move Jetpack data","jetpack-connection")),m().createElement("p",null,(0,c.createInterpolateElement)(h.migrateCardBodyText||(0,i.sprintf)(/* translators: %1$s: The current site domain name. %2$s: The original site domain name. */
__("Move all your settings, stats and subscribers to your other URL, <hostname>%1$s</hostname>. <hostname>%2$s</hostname> will be disconnected from Jetpack.","jetpack-connection"),n,t),{hostname:m().createElement("strong",null),em:m().createElement("em",null),strong:m().createElement("strong",null)}))),m().createElement("div",{className:"jp-idc__idc-screen__card-action-bottom"},m().createElement("div",{className:"jp-idc__idc-screen__card-action-sitename"},t),m().createElement(a.Dashicon,{icon:"arrow-down-alt",className:"jp-idc__idc-screen__card-action-separator"}),m().createElement("div",{className:"jp-idc__idc-screen__card-action-sitename"},n),m().createElement(a.Button,{className:"jp-idc__idc-screen__card-action-button",label:y,onClick:p,disabled:l},d?m().createElement(s.A,null):y),k&&(v=h.supportURL,m().createElement(f.A,null,(0,c.createInterpolateElement)(__("Could not move your settings. Retry or find out more <a>here</a>.","jetpack-connection"),{a:m().createElement("a",{href:v||(0,r.A)("jetpack-support-safe-mode"),rel:"noopener noreferrer",target:"_blank"})})))));var v};k.propTypes={wpcomHomeUrl:d().string.isRequired,currentUrl:d().string.isRequired,isMigrating:d().bool,migrateCallback:d().func,customContent:d().shape(h.A),hasError:d().bool};const y=k},9882:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});var r=n(1609),s=n.n(r);const a=()=>s().createElement("svg",{className:"error-gridicon",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",height:24},s().createElement("rect",{x:"0",fill:"none",width:"24",height:"24"}),s().createElement("g",null,s().createElement("path",{d:"M12 4c4.411 0 8 3.589 8 8s-3.589 8-8 8-8-3.589-8-8 3.589-8 8-8m0-2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2zm1 13h-2v2h2v-2zm-2-2h2l.5-6h-3l.5 6z"})))},6576:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});var r=n(1609),s=n.n(r),a=n(9882);n(2145);const o=e=>{const{children:t}=e;return s().createElement("div",{className:"jp-idc__error-message"},s().createElement(a.A,null),s().createElement("span",null,t))}},7459:(e,t,n)=>{"use strict";n.d(t,{A:()=>k});var r=n(5932),s=n(7143),a=n(3619),o=n.n(a),c=n(1609),i=n.n(c),l=n(8101),d=n(5574),p=n(8502),m=n(3207),u=n(1057),h=n(2879),g=n(8979);const f=e=>{const{logo:t,customContent:n={},wpcomHomeUrl:a,currentUrl:o,apiNonce:u,apiRoot:f,redirectUri:k,tracksUserData:y,tracksEventData:v,isAdmin:_,possibleDynamicSiteUrlDetected:b,isDevelopmentSite:C}=e,[E,w]=(0,c.useState)(!1),j=(0,s.useSelect)((e=>e(m.a).getErrorType()),[]),{isMigrating:S,migrateCallback:A}=(0,l.A)((0,c.useCallback)((()=>{w(!0)}),[w])),{isStartingFresh:F,startFreshCallback:I}=(0,p.A)(k),{isFinishingMigration:U,finishMigrationCallback:T}=(0,d.A)();return(0,c.useEffect)((()=>{r.Ay.setApiRoot(f),r.Ay.setApiNonce(u),(0,h.f)(v,y),v&&(Object.hasOwn(v,"isAdmin")&&v.isAdmin?(0,h.A)("notice_view"):(0,h.A)("non_admin_notice_view",{page:!!Object.hasOwn(v,"currentScreen")&&v.currentScreen}))}),[f,u,y,v]),i().createElement(g.A,{logo:t,customContent:n,wpcomHomeUrl:a,currentUrl:o,redirectUri:k,isMigrating:S,migrateCallback:A,isMigrated:E,finishMigrationCallback:T,isFinishingMigration:U,isStartingFresh:F,startFreshCallback:I,isAdmin:_,hasStaySafeError:"safe-mode"===j,hasFreshError:"start-fresh"===j,hasMigrateError:"migrate"===j,possibleDynamicSiteUrlDetected:b,isDevelopmentSite:C})};f.propTypes={logo:o().oneOfType([o().string,o().object]),customContent:o().shape(u.A),wpcomHomeUrl:o().string.isRequired,currentUrl:o().string.isRequired,redirectUri:o().string.isRequired,apiRoot:o().string.isRequired,apiNonce:o().string.isRequired,tracksUserData:o().object,tracksEventData:o().object,isAdmin:o().bool.isRequired,possibleDynamicSiteUrlDetected:o().bool,isDevelopmentSite:o().bool};const k=f},1217:(e,t,n)=>{"use strict";n.d(t,{A:()=>f});var r=n(3924),s=n(6087),a=n(7723),o=n(3619),c=n.n(o),i=n(1609),l=n.n(i),d=n(1057),p=n(6043),m=n(3685),u=n(6930),h=n(1133);const __=a.__,g=e=>{const{wpcomHomeUrl:t,currentUrl:n,isMigrating:o=!1,migrateCallback:c,isStartingFresh:i=!1,startFreshCallback:d,customContent:g={},hasMigrateError:f=!1,hasFreshError:k=!1,hasStaySafeError:y=!1,possibleDynamicSiteUrlDetected:v=!1,isDevelopmentSite:_}=e,b=(0,p.A)(e.wpcomHomeUrl),C=(0,p.A)(e.currentUrl);return l().createElement(l().Fragment,null,l().createElement("h2",null,g.mainTitle?(0,s.createInterpolateElement)(g.mainTitle,{em:l().createElement("em",null)}):__("Safe Mode has been activated","jetpack-connection")),l().createElement("p",null,_?(0,s.createInterpolateElement)(g.mainBodyTextDev||(0,a.sprintf)(/* translators: %1$s: The current site domain name. %2$s: The original site domain name. */
__("<span>Your site is in Safe Mode because <hostname>%1$s</hostname> appears to be a staging or development copy of <hostname>%2$s</hostname>.</span>Two sites that are telling Jetpack they’re the same site. <safeModeLink>Learn more or troubleshoot common Safe mode issues</safeModeLink>.","jetpack-connection"),C,b),{span:l().createElement("span",{style:{display:"block"}}),hostname:l().createElement("strong",null),em:l().createElement("em",null),strong:l().createElement("strong",null),safeModeLink:l().createElement("a",{href:g.supportURL||(0,r.A)("jetpack-support-safe-mode"),rel:"noopener noreferrer",target:"_blank"})}):(0,s.createInterpolateElement)(g.mainBodyText||__("Your site is in Safe Mode because you have 2 Jetpack-powered sites that appear to be duplicates. Two sites that are telling Jetpack they’re the same site. <safeModeLink>Learn more about safe mode.</safeModeLink>","jetpack-connection"),{safeModeLink:l().createElement("a",{href:g.supportURL||(0,r.A)("jetpack-support-safe-mode"),rel:"noopener noreferrer",target:"_blank"}),em:l().createElement("em",null),strong:l().createElement("strong",null)})),v&&l().createElement("p",null,(0,s.createInterpolateElement)(g.dynamicSiteUrlText||__("<strong>Notice:</strong> It appears that your 'wp-config.php' file might be using dynamic site URL values. Dynamic site URLs could cause Jetpack to enter Safe Mode. <dynamicSiteUrlSupportLink>Learn how to set a static site URL.</dynamicSiteUrlSupportLink>","jetpack-connection"),{dynamicSiteUrlSupportLink:l().createElement("a",{href:g.dynamicSiteUrlSupportLink||(0,r.A)("jetpack-idcscreen-dynamic-site-urls"),rel:"noopener noreferrer",target:"_blank"}),em:l().createElement("em",null),strong:l().createElement("strong",null)})),l().createElement("h3",null,__("Please select an option","jetpack-connection")),l().createElement("div",{className:"jp-idc__idc-screen__cards"+(f||k?" jp-idc__idc-screen__cards-error":"")},_?l().createElement(l().Fragment,null,l().createElement(m.A,{wpcomHomeUrl:t,currentUrl:n,isStartingFresh:i,startFreshCallback:d,customContent:g,hasError:k,isDevelopmentSite:_}),l().createElement("div",{className:"jp-idc__idc-screen__cards-separator"},"or"),l().createElement(h.A,{hasError:y,customContent:g,isDevelopmentSite:_})):l().createElement(l().Fragment,null,l().createElement(u.A,{wpcomHomeUrl:t,currentUrl:n,isMigrating:o,migrateCallback:c,customContent:g,hasError:f}),l().createElement("div",{className:"jp-idc__idc-screen__cards-separator"},"or"),l().createElement(m.A,{wpcomHomeUrl:t,currentUrl:n,isStartingFresh:i,startFreshCallback:d,customContent:g,hasError:k,isDevelopmentSite:_}))),_?null:l().createElement(h.A,{hasError:y,customContent:g}))};g.propTypes={wpcomHomeUrl:c().string.isRequired,currentUrl:c().string.isRequired,isMigrating:c().bool,migrateCallback:c().func,isStartingFresh:c().bool,startFreshCallback:c().func,customContent:c().shape(d.A),hasMigrateError:c().bool,hasFreshError:c().bool,hasStaySafeError:c().bool,possibleDynamicSiteUrlDetected:c().bool,isDevelopmentSite:c().bool};const f=g},4295:(e,t,n)=>{"use strict";n.d(t,{A:()=>h});var r=n(6461),s=n(6427),a=n(6087),o=n(7723),c=n(3619),i=n.n(c),l=n(1609),d=n.n(l),p=n(1057),m=n(6043);const __=o.__,u=e=>{const{finishCallback:t=()=>{},isFinishing:n=!1,customContent:c={}}=e,i=(0,m.A)(e.wpcomHomeUrl),l=(0,m.A)(e.currentUrl),p=__("Got it, thanks","jetpack-connection");return d().createElement(d().Fragment,null,d().createElement("h2",null,c.migratedTitle?(0,a.createInterpolateElement)(c.migratedTitle,{em:d().createElement("em",null)}):__("Your Jetpack settings have migrated successfully","jetpack-connection")),d().createElement("p",null,(0,a.createInterpolateElement)(c.migratedBodyText||(0,o.sprintf)(/* translators: %1$s: The current site domain name. */
__("Safe Mode has been switched off for <hostname>%1$s</hostname> website and Jetpack is fully functional.","jetpack-connection"),l),{hostname:d().createElement("strong",null),em:d().createElement("em",null),strong:d().createElement("strong",null)})),d().createElement("div",{className:"jp-idc__idc-screen__card-migrated"},d().createElement("div",{className:"jp-idc__idc-screen__card-migrated-hostname"},i),d().createElement(s.Dashicon,{icon:"arrow-down-alt",className:"jp-idc__idc-screen__card-migrated-separator"}),d().createElement(s.Dashicon,{icon:"arrow-right-alt",className:"jp-idc__idc-screen__card-migrated-separator-wide"}),d().createElement("div",{className:"jp-idc__idc-screen__card-migrated-hostname"},l)),d().createElement(s.Button,{className:"jp-idc__idc-screen__card-action-button jp-idc__idc-screen__card-action-button-migrated",onClick:t,label:p},n?d().createElement(r.A,null):p))};u.propTypes={wpcomHomeUrl:i().string.isRequired,currentUrl:i().string.isRequired,finishCallback:i().func,isFinishing:i().bool,customContent:i().shape(p.A)};const h=u},9291:(e,t,n)=>{"use strict";n.d(t,{A:()=>m});var r=n(3924),s=n(6087),a=n(7723),o=n(3619),c=n.n(o),i=n(1609),l=n.n(i),d=n(1057);const __=a.__,p=e=>{const{customContent:t={}}=e;return l().createElement(l().Fragment,null,l().createElement("h2",null,t.nonAdminTitle?(0,s.createInterpolateElement)(t.nonAdminTitle,{em:l().createElement("em",null)}):__("Safe Mode has been activated","jetpack-connection")),l().createElement("p",null,(0,s.createInterpolateElement)(t.nonAdminBodyText||__("This site is in Safe Mode because there are 2 Jetpack-powered sites that appear to be duplicates. 2 sites that are telling Jetpack they’re the same site. <safeModeLink>Learn more about safe mode.</safeModeLink>","jetpack-connection"),{safeModeLink:l().createElement("a",{href:t.supportURL||(0,r.A)("jetpack-support-safe-mode"),rel:"noopener noreferrer",target:"_blank"}),em:l().createElement("em",null),strong:l().createElement("strong",null)})),t.nonAdminBodyText?"":l().createElement("p",null,__("An administrator of this site can take Jetpack out of Safe Mode.","jetpack-connection")))};p.propTypes={customContent:c().shape(d.A)};const m=p},8979:(e,t,n)=>{"use strict";n.d(t,{A:()=>g});var r=n(7142),s=n(6087),a=n(7723),o=n(3619),c=n.n(o),i=n(1609),l=n.n(i),d=n(1057),p=n(1217),m=n(4295),u=n(9291);n(9958);const __=a.__,h=e=>{const{logo:t=l().createElement(r.A,{height:24}),customContent:n={},wpcomHomeUrl:a,currentUrl:o,redirectUri:c,isMigrating:i=!1,migrateCallback:d,isMigrated:h=!1,finishMigrationCallback:g,isFinishingMigration:f=!1,isStartingFresh:k=!1,startFreshCallback:y,isAdmin:v,hasMigrateError:_=!1,hasFreshError:b=!1,hasStaySafeError:C=!1,possibleDynamicSiteUrlDetected:E=!1,isDevelopmentSite:w}=e,j=v?"":l().createElement(u.A,{customContent:n});let S="";return v&&(S=h?l().createElement(m.A,{wpcomHomeUrl:a,currentUrl:o,finishCallback:g,isFinishing:f,customContent:n}):l().createElement(p.A,{wpcomHomeUrl:a,currentUrl:o,redirectUri:c,customContent:n,isMigrating:i,migrateCallback:d,isStartingFresh:k,startFreshCallback:y,hasMigrateError:_,hasFreshError:b,hasStaySafeError:C,possibleDynamicSiteUrlDetected:E,isDevelopmentSite:w})),l().createElement("div",{className:"jp-idc__idc-screen"+(h?" jp-idc__idc-screen__success":"")},l().createElement("div",{className:"jp-idc__idc-screen__header"},l().createElement("div",{className:"jp-idc__idc-screen__logo"},((e,t)=>"string"==typeof e||e instanceof String?l().createElement("img",{src:e,alt:t,className:"jp-idc__idc-screen__logo-image"}):e)(t,n.logoAlt||"")),l().createElement("div",{className:"jp-idc__idc-screen__logo-label"},n.headerText?(0,s.createInterpolateElement)(n.headerText,{em:l().createElement("em",null),strong:l().createElement("strong",null)}):__("Safe Mode","jetpack-connection"))),j,S)};h.propTypes={logo:c().oneOfType([c().string,c().object]),customContent:c().shape(d.A),wpcomHomeUrl:c().string.isRequired,currentUrl:c().string.isRequired,redirectUri:c().string.isRequired,isMigrating:c().bool,migrateCallback:c().func,isMigrated:c().bool,finishMigrationCallback:c().func,isFinishingMigration:c().bool,isStartingFresh:c().bool,startFreshCallback:c().func,isAdmin:c().bool.isRequired,hasMigrateError:c().bool,hasFreshError:c().bool,hasStaySafeError:c().bool,possibleDynamicSiteUrlDetected:c().bool,isDevelopmentSite:c().bool};const g=h},1133:(e,t,n)=>{"use strict";n.d(t,{A:()=>C});var r=n(5932),s=n(6461),a=n(3924),o=n(6427),c=n(9491),i=n(7143),l=n(6087),d=n(7723),p=n(3832),m=n(3619),u=n.n(m),h=n(1609),g=n.n(h),f=n(3207),k=n(1057),y=n(2879),v=n(6576);n(9626);const __=d.__,_=e=>g().createElement(v.A,null,(0,l.createInterpolateElement)(__("Could not stay in safe mode. Retry or find out more <a>here</a>.","jetpack-connection"),{a:g().createElement("a",{href:e||(0,a.A)("jetpack-support-safe-mode"),rel:"noopener noreferrer",target:"_blank"})})),b=e=>{const{isActionInProgress:t,setIsActionInProgress:n,setErrorType:c,clearErrorType:i,hasError:d=!1,customContent:m,isDevelopmentSite:u}=e,[f,k]=(0,h.useState)(!1),v=m.stayInSafeModeButtonLabel||__("Stay in Safe mode","jetpack-connection"),b=(0,h.useCallback)((()=>{t||(k(!0),n(!0),i(),(0,y.A)("confirm_safe_mode"),r.Ay.confirmIDCSafeMode().then((()=>{window.location.href=(0,p.removeQueryArgs)(window.location.href,"jetpack_idc_clear_confirmation","_wpnonce")})).catch((e=>{throw n(!1),k(!1),c("safe-mode"),e})))}),[t,n,c,i]);return g().createElement(g().Fragment,null,u?g().createElement("div",{className:"jp-idc__idc-screen__card-action-base"+(d?" jp-idc__idc-screen__card-action-error":"")},g().createElement("div",{className:"jp-idc__idc-screen__card-action-top"},g().createElement("h4",null,m.safeModeTitle?(0,l.createInterpolateElement)(m.safeModeTitle,{em:g().createElement("em",null)}):__("Stay in Safe Mode","jetpack-connection")),g().createElement("div",null,(0,l.createInterpolateElement)(m.safeModeCardBodyText||/* translators: %1$s: The current site domain name. %2$s: The original site domain name. */
__("<p><strong>Recommended for</strong></p><list><item>short-lived test sites</item><item>sites that will be cloned back to production after testing</item></list><p><strong>Please note</strong> that staying in Safe mode will disable some Jetpack features, including security features such as SSO, firewall, and site monitor. <safeModeLink>Learn more</safeModeLink>.</p>","jetpack-connection"),{p:g().createElement("p",null),hostname:g().createElement("strong",null),em:g().createElement("em",null),strong:g().createElement("strong",null),list:g().createElement("ul",null),item:g().createElement("li",null),safeModeLink:g().createElement("a",{href:m.supportURL||(0,a.A)("jetpack-support-safe-mode"),rel:"noopener noreferrer",target:"_blank"})}))),g().createElement("div",{className:"jp-idc__idc-screen__card-action-bottom"},g().createElement(o.Button,{className:"jp-idc__idc-screen__card-action-button-secondary",label:v,onClick:b,disabled:t},f?g().createElement(s.A,{color:"black"}):v),d&&_(m.supportURL))):g().createElement("div",{className:"jp-idc__safe-mode"},f?g().createElement("div",{className:"jp-idc__safe-mode__staying-safe"},g().createElement(s.A,{color:"black"}),g().createElement("span",null,__("Finishing setting up Safe mode…","jetpack-connection"))):(C=b,E=t,(0,l.createInterpolateElement)(__("Or decide later and stay in <button>Safe mode</button>","jetpack-connection"),{button:g().createElement(o.Button,{label:__("Safe mode","jetpack-connection"),variant:"link",onClick:C,disabled:E})})),d&&_(m.supportURL)));var C,E};b.propTypes={isActionInProgress:u().bool,setIsActionInProgress:u().func.isRequired,setErrorType:u().func.isRequired,clearErrorType:u().func.isRequired,hasError:u().bool,customContent:u().shape(k.A),isDevelopmentSite:u().bool};const C=(0,c.compose)([(0,i.withSelect)((e=>({isActionInProgress:e(f.a).getIsActionInProgress()}))),(0,i.withDispatch)((e=>({setIsActionInProgress:e(f.a).setIsActionInProgress,setErrorType:e(f.a).setErrorType,clearErrorType:e(f.a).clearErrorType})))])(b)},5574:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609);const s=()=>{const[e,t]=(0,r.useState)(!1),n=(0,r.useCallback)((()=>{e||(t(!0),window.location.reload())}),[e,t]);return{isFinishingMigration:e,finishMigrationCallback:n}}},8101:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var r=n(5932),s=n(7143),a=n(1609),o=n(3207),c=n(2879);const i=e=>{const[t,n]=(0,a.useState)(!1),i=(0,s.useSelect)((e=>e(o.a).getIsActionInProgress()),[]),{setIsActionInProgress:l,setErrorType:d,clearErrorType:p}=(0,s.useDispatch)(o.a);return{isMigrating:t,migrateCallback:(0,a.useCallback)((()=>{i||((0,c.A)("migrate"),l(!0),n(!0),p(),r.Ay.migrateIDC().then((()=>{n(!1),e&&"[object Function]"==={}.toString.call(e)&&e()})).catch((e=>{throw l(!1),n(!1),d("migrate"),e})))}),[n,e,i,l,d,p])}}},8502:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var r=n(5932),s=n(7143),a=n(1609),o=n(3207),c=n(2879);const i=e=>{const[t,n]=(0,a.useState)(!1),i=(0,s.useSelect)((e=>e(o.a).getIsActionInProgress()),[]),{setIsActionInProgress:l,setErrorType:d,clearErrorType:p}=(0,s.useDispatch)(o.a);return{isStartingFresh:t,startFreshCallback:(0,a.useCallback)((()=>{i||((0,c.A)("start_fresh"),l(!0),n(!0),p(),r.Ay.startIDCFresh(e).then((e=>{window.location.href=e+"&from=idc-notice"})).catch((e=>{throw l(!1),n(!1),d("start-fresh"),e})))}),[n,i,l,e,d,p])}}},8269:(e,t,n)=>{"use strict";n.d(t,{Ay:()=>o,Xs:()=>r,sL:()=>a,xj:()=>s});const r="SET_IS_ACTION_IN_PROGRESS",s="SET_ERROR_TYPE",a="CLEAR_ERROR_TYPE",o={setIsActionInProgress:e=>({type:r,isInProgress:e}),setErrorType:e=>({type:s,errorType:e}),clearErrorType:()=>({type:a})}},2093:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});var r=n(7143),s=n(8269);const a=(0,r.combineReducers)({isActionInProgress:(e=!1,t)=>t.type===s.Xs?t.isInProgress:e,errorType:(e=null,t)=>{switch(t.type){case s.xj:return t.errorType;case s.sL:return null}return e}})},8918:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});const r={getIsActionInProgress:e=>e.isActionInProgress||!1,getErrorType:e=>e.errorType||null}},1908:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});var r=n(7143);class s{static store=null;static mayBeInit(e,t){null===s.store&&(s.store=(0,r.createReduxStore)(e,t),(0,r.register)(s.store))}}const a=s},3207:(e,t,n)=>{"use strict";n.d(t,{a:()=>c});var r=n(8269),s=n(2093),a=n(8918),o=n(1908);const c="jetpack-idc";o.A.mayBeInit(c,{reducer:s.A,actions:r.Ay,selectors:a.A})},1057:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});var r=n(3619),s=n.n(r);const a={headerText:s().string,logoAlt:s().string,mainTitle:s().string,mainBodyText:s().string,mainBodyTextDev:s().string,migratedTitle:s().string,migratedBodyText:s().string,migrateCardTitle:s().string,migrateButtonLabel:s().string,migrateCardBodyText:s().string,startFreshCardTitle:s().string,startFreshCardBodyText:s().string,safeModeCardBodyText:s().string,startFreshCardBodyTextDev:s().string,startFreshButtonLabel:s().string,nonAdminTitle:s().string,nonAdminBodyText:s().string,supportURL:s().string,stayInSafeModeButtonLabel:s().string}},6043:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});const r=e=>/^https?:\/\//.test(e)?new URL(e).hostname:e.replace(/\/$/,"")},2879:(e,t,n)=>{"use strict";n.d(t,{A:()=>a,f:()=>s});var r=n(372);function s(e,t){t&&Object.hasOwn(t,"userid")&&Object.hasOwn(t,"username")&&r.A.initialize(t.userid,t.username),e&&(Object.hasOwn(e,"blogID")&&r.A.assignSuperProps({blog_id:e.blogID}),Object.hasOwn(e,"platform")&&r.A.assignSuperProps({platform:e.platform})),r.A.setMcAnalyticsEnabled(!0)}function a(e,t={}){void 0!==t&&"object"==typeof t||(t={}),e&&e.length&&void 0!==r.A&&r.A.tracks&&r.A.mc&&(e=0!==(e=e.replace(/-/g,"_")).indexOf("jetpack_idc_")?"jetpack_idc_"+e:e,r.A.tracks.recordEvent(e,t),e=(e=e.replace("jetpack_idc_","")).replace(/_/g,"-"),r.A.mc.bumpStat("jetpack-idc",e))}},9074:e=>{"use strict";e.exports={consumer_slug:"connection_package"}},1609:e=>{"use strict";e.exports=window.React},6427:e=>{"use strict";e.exports=window.wp.components},9491:e=>{"use strict";e.exports=window.wp.compose},7143:e=>{"use strict";e.exports=window.wp.data},6087:e=>{"use strict";e.exports=window.wp.element},7723:e=>{"use strict";e.exports=window.wp.i18n},3832:e=>{"use strict";e.exports=window.wp.url},8579:e=>{function t(){return e.exports=t=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},e.exports.__esModule=!0,e.exports.default=e.exports,t.apply(null,arguments)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},2231:(e,t,n)=>{"use strict";function r(e){var t,n,s="";if("string"==typeof e||"number"==typeof e)s+=e;else if("object"==typeof e)if(Array.isArray(e)){var a=e.length;for(t=0;t<a;t++)e[t]&&(n=r(e[t]))&&(s&&(s+=" "),s+=n)}else for(n in e)e[n]&&(s&&(s+=" "),s+=n);return s}n.d(t,{A:()=>s});const s=function(){for(var e,t,n=0,s="",a=arguments.length;n<a;n++)(e=arguments[n])&&(t=r(e))&&(s&&(s+=" "),s+=t);return s}}},t={};function n(r){var s=t[r];if(void 0!==s)return s.exports;var a=t[r]={exports:{}};return e[r](a,a.exports,n),a.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{"use strict";var e=n(7459),t=n(6087),r=n(1609),s=n.n(r);window.addEventListener("load",(()=>function(){if(!Object.hasOwn(window,"JP_IDENTITY_CRISIS__INITIAL_STATE"))return;const n=document.getElementById(window.JP_IDENTITY_CRISIS__INITIAL_STATE.containerID||"jp-identity-crisis-container");if(null===n)return;const{WP_API_root:r,WP_API_nonce:a,wpcomHomeUrl:o,currentUrl:c,redirectUri:i,tracksUserData:l,tracksEventData:d,isSafeModeConfirmed:p,consumerData:m,isAdmin:u,possibleDynamicSiteUrlDetected:h,isDevelopmentSite:g}=window.JP_IDENTITY_CRISIS__INITIAL_STATE;if(!p){const p=s().createElement(e.A,{wpcomHomeUrl:o,currentUrl:c,apiRoot:r,apiNonce:a,redirectUri:i,tracksUserData:l||{},tracksEventData:d,customContent:Object.hasOwn(m,"customContent")?m.customContent:{},isAdmin:u,logo:Object.hasOwn(m,"logo")?m.logo:void 0,possibleDynamicSiteUrlDetected:h,isDevelopmentSite:g});t.createRoot(n).render(p)}}()))})()})();